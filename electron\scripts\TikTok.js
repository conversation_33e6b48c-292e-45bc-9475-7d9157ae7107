const ipc = window.electronAPI;
let userInfo = {};
let trcConfig = {}

const getCurrentUserId = () => {
    let userId;
    // 获取 data-e2e="chat-uniqueid" 的 p 元素
    const uniqueIdElement = document.querySelector('[data-e2e="chat-uniqueid"]');
    // 检查元素是否存在
    if (uniqueIdElement) {
        // 获取该 p 元素的文本内容（即 @xxe39821b5j）
        userId = uniqueIdElement.textContent;
    }
    return userId;
}
const getNewMsgCount = () => {
    let msgCount = 0; // 全局消息计数器
    try {
        // 获取消息数量的 <sup> 标签
        const msgBadge = document.querySelector('[data-e2e="top-dm-icon"] .css-r2juw9-SupMsgBadge');

        // 如果找到 <sup> 标签，获取其文本内容，否则设置为 0
        let newMsgCount = msgBadge ? parseInt(msgBadge.textContent, 10) : 0;

        // 累加消息数量
        if (msgCount !== newMsgCount) {
            msgCount = newMsgCount; // 更新全局消息计数器
        }
    } catch (e) {
        console.error("获取新消息数量发生错误：", e.message);
    }
    return msgCount;
};
const getAvatarImg = ()=> {
    // 获取 #header-more-menu-icon 节点
    const element = document.querySelector("#header-more-menu-icon");
    // 获取该元素的背景图片的 URL
    const backgroundImageUrl = element.style.backgroundImage;
    // 提取 URL 部分
    const url = backgroundImageUrl.slice(5, -2); // 去掉 "url(" 和 ")" 字符
    return url
}
const onlineStatusCheck = ()=> {
    setInterval(async () => {
        let userSessionStr = sessionStorage.user_session;
        if (userSessionStr) {
            // 假设从存储中获取的 user_session 值
            const userSession = JSON.parse(userSessionStr);
            // 判断 uid 是否为 "0" 来确定是否登录
            let onlineStatus = !(userSession && userSession.uid === "0");
            const avatarUrl = getAvatarImg();
            const msgCount = getNewMsgCount()
            const args = {
                platform: 'TikTok',
                onlineStatus: onlineStatus,
                avatarUrl: avatarUrl,
                nickName:'',
                phoneNumber:userSession.uid,
                userName:'',
                msgCount:msgCount,
            }
            ipc.loginNotify(args);
            userInfo = args;
        } else {
            const args = {
                platform: 'TikTok',
                onlineStatus: false,
                avatarUrl: '',
                nickName:'',
                phoneNumber:'',
                userName:'',
                msgCount:0
            }
            ipc.loginNotify(args);
            userInfo = args;
        }
    }, 3000); // 每隔5000毫秒（5秒）调用一次
}

onlineStatusCheck();

const sendMsg = ()=> {
    const element = document.querySelector("svg[data-e2e=message-send]");
    if (element){
        // 创建一个点击事件
        const clickEvent = new MouseEvent('click', {
            bubbles: true,    // 事件是否冒泡
            cancelable: true, // 事件是否可以取消
        });
        // 触发事件
        element.dispatchEvent(clickEvent);
    }else {
        console.error("发送按钮节点无效")
    }
}
const getMsgText = (node) => {
    const spans = node.querySelectorAll('span [data-text=true]');
    // 用于存储每一行的内容
    let content = [];
    spans.forEach(span => {
        // 获取 span 的文本内容并去除前后空格
        const text = span.textContent.trim();
        // 如果内容为空，则添加空字符串，否则添加文本内容
        content.push(text === '' ? '' : text);
    });
    // 将内容数组拼接成一个字符串，用换行符分隔
    return content.join('\n');
};
const sendTranslate = async (text,to)=>{
    if (text && text.trim()) {
        const route = trcConfig.translateRoute;
        const mode = trcConfig.mode;
        styledTextarea.setContent('翻译中...')
        styledTextarea.setIsProcessing(true);
        // console.log('发送翻译请求：')
        const res = await ipc.translateText({route: route, text: text, to: to,isFilter:'true',mode:mode});
        if (res.status) {
            //调用ipc获取翻译结果
            styledTextarea.setContent(res.data);
            styledTextarea.setTranslateStatus(true)
            styledTextarea.setIsProcessing(false);
        }else {
            styledTextarea.setContent(res.message);
            styledTextarea.setTranslateStatus(false)
            styledTextarea.setIsProcessing(false);
        }
    }
}
const createStyledTextarea = () => {
    const container = document.createElement('div');
    Object.assign(container.style, {
        display: 'flex',
        flexDirection: 'column',
    });
    container.id = 'custom-translate-textarea';
    container.classList.add('x1bmpntp');
    container.style.boxSizing = 'border-box'
    // 标题元素
    const label = document.createElement('span');
    Object.assign(label.style, {
        fontSize: '12px',
        color: 'green',
        marginLeft: '20px',
    });
    label.textContent = ''
    // 文本域容器
    const textareaWrapper = document.createElement('div');
    textareaWrapper.style.overflowY = 'auto';
    textareaWrapper.style.maxHeight = '100px';
    const textarea = document.createElement('textarea');
    textarea.style.color = 'var(--ui-text-1)'
    Object.assign(textarea.style, {
        fontSize: '12px',
        opacity: '0.7',  // 添加这行来设置透明度
        width: '100%',
        border: 'none',
        resize: 'none',
        overflow: 'hidden',
        boxSizing: 'border-box',
        paddingLeft: '20px',
        paddingRight: '40px',
        outline: 'none',
        overflowY: 'auto',
        pointerEvents: 'none', // 禁用交互
    });
    textarea.tabIndex = -1;  // 禁用键盘聚焦
    textarea.value = '...';
    // 自动高度调整
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // 组装元素
    textareaWrapper.appendChild(textarea);
    container.appendChild(label);
    container.appendChild(textareaWrapper);
    //翻译状态
    let translateStatus = false;
    let isProcessing = false;
    //初始化属性数据
    const initData = () =>{
        translateStatus = false;
        isProcessing = false;
        textarea.value = '...';
        // 手动触发高度调整
        const event = new Event('input', { bubbles: true });
        textarea.dispatchEvent(event);
    }
    // 暴露设置方法
    const setContent = (content) => {
        if (content) {
            textarea.value = content;
            // 手动触发高度调整
            const event = new Event('input', { bubbles: true });
            textarea.dispatchEvent(event);
        }
    };
    const getContent = () => {
        return textarea.value;
    };
    const setTitle = (title) => {
        if (title) label.textContent = title;
    };
    const setTranslateStatus = (status) => {
        translateStatus = status;
    };
    const getTranslateStatus = () => {
        return translateStatus; // 直接返回状态值
    };
    const setIsProcessing = (status) => {
        isProcessing = status;
    };
    const getIsProcessing = () => {
        return isProcessing; // 直接返回状态值
    };

    return {
        initData,
        container,    // DOM节点
        setTitle,   //设置翻译线路
        setContent,    // 设置内容的方法
        setTranslateStatus,//设置翻译状态
        getTranslateStatus,
        getIsProcessing,
        setIsProcessing,
        getContent
    };
};
const styledTextarea = createStyledTextarea()
const addTranslatePreview = ()=>{
    const footerDiv = document.querySelector("div [data-e2e=message-input-area]")
    const msgDiv = footerDiv.parentNode
    if (msgDiv) {
        // 创建父容器
        const container = document.createElement('div');
        msgDiv.parentNode.insertBefore(container, msgDiv);
        container.appendChild(styledTextarea.container);
        container.appendChild(msgDiv);
    }
}
const addTranslateListener = () => {
    // 获取元素
    const editableDiv = document.querySelector('div[data-e2e="message-input-area"]');
    // 1️⃣ 移除旧的监听器（如果存在）
    if (editableDiv._previousKeydownHandler) {
        editableDiv.removeEventListener('keydown', editableDiv._previousKeydownHandler);
    }
    // 防抖函数
    const debounce = (func, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId); // 清除之前的定时器
            timeoutId = setTimeout(() => func.apply(this, args), delay); // 设置新的定时器
        };
    };
    // 处理输入事件的逻辑
    const handleInput = async () => {
        const flag = trcConfig.translatePreview;
        const textContent = getMsgText(editableDiv)
        const sendStatus = trcConfig.sendTranslateStatus;
        if(textContent === '' || !textContent) {
            styledTextarea.setContent('...')
            return;
        }
        const isProcessing = styledTextarea.getIsProcessing();
        if (flag && flag === 'true' && sendStatus === 'true' && isProcessing === false) {
            const to = trcConfig.sendTargetLanguage;
            styledTextarea.setTranslateStatus(false)
            await sendTranslate(textContent,to);
        }
    };
    // 键盘回车事件函数
    const handleKeyDown = async (event) => {
        debouncedHandleInput()
        if (event.key === 'Enter') {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            if (isProcessing) return;
            const sendTranslateStatus = trcConfig.sendTranslateStatus;
            const sendPreview = trcConfig.translatePreview;
            const textContent = getMsgText(editableDiv);
            const from = trcConfig.sendSourceLanguage;
            const to = trcConfig.sendTargetLanguage;
            const route = trcConfig.translateRoute;
            const mode = trcConfig.mode;
            const args = {text:textContent,from:from,to: to,route:route,mode:mode};
            if (sendPreview === 'true'&& sendTranslateStatus === 'true') {
                const status = styledTextarea.getTranslateStatus()
                const isProcess = styledTextarea.getIsProcessing()
                if (status === true && isProcess === false) {
                    const translateText = styledTextarea.getContent();
                    // 获取所有需要翻译的文本元素
                    let msgElements =  document.querySelectorAll('span[data-text=true]');
                    //判断发送的内容是否全是表情
                    if (msgElements.length<=0) {
                        sendMsg()
                        return;
                    }
                    await inputMsg(translateText)
                    // 延迟确保状态更新
                    setTimeout(() => {
                        sendMsg();
                        styledTextarea.setTranslateStatus(false);
                        styledTextarea.setContent('...');
                    }, 100);
                }
            }else if (textContent && textContent !=='' && sendTranslateStatus === 'true') {
                isProcessing = true;
                styledTextarea.setContent('翻译中...')
                const res = await ipc.translateText(args)
                if (res.status) {
                    const translateText = res.data;
                    await inputMsg(translateText)
                    setTimeout(()=>{
                        sendMsg();
                    },500)
                    isProcessing = false;
                    styledTextarea.setTranslateStatus(false)
                    styledTextarea.setContent('...')
                }else {
                    styledTextarea.setContent(res.message);
                    isProcessing = false;
                }
            }else {
                sendMsg();
            }
        }
    }
    // 创建防抖后的 handleInput 函数
    const debouncedHandleInput = debounce(handleInput, 500);

    let isProcessing = false;
    // 3️⃣ 保存当前处理函数的引用
    editableDiv._previousKeydownHandler = handleKeyDown;
    editableDiv.addEventListener('keydown', handleKeyDown);

};
const updateConfigInfo = async () => {
    const currentUserId = getCurrentUserId();
    const args = {platform: 'TikTok', userId: currentUserId || ''};
    const res = await ipc.getTranslateConfig(args)
    if (res.status) {
        styledTextarea.setTitle(res.data.zhName);
        trcConfig = res.data;
    }else {
        trcConfig = {};
        console.error('获取配置信息失败：',res);
    }
}

//会话列表切换触发函数
const sessionChange = async () => {
    const currentUserId = getCurrentUserId();
    const args = {platform: 'TikTok', userId: currentUserId};
    ipc.infoUpdate(args)
    await updateConfigInfo()
    const myNode = document.getElementById('custom-translate-textarea')
    if (!myNode) {
        addTranslatePreview();
        addTranslateListener();
    }
    styledTextarea.initData()
}
const debouncedSessionChange = debounce(sessionChange,200);

const monitorMainNode = ()=> {
    // 监听整个 body 的 DOM 变化，等待 #main 节点的出现
    const observer = new MutationObserver(async (mutationsList, observer) => {
        for (let mutation of mutationsList) {
            if (mutation.type === 'childList') {
                //设置授权码
                // 检查是否已经存在 id="main" 的节点
                const mainNode = document.getElementById('main-content-messages');
                if (mainNode) {
                    // 停止对 body 的观察，避免不必要的性能开销
                    observer.disconnect();
                    // 开始监听 #main 节点的子节点变化
                    let leftNode = document.querySelector("div.css-149gota-DivScrollWrapper")
                    if (leftNode) {
                        observePaneSide(leftNode);
                    }else {
                        console.error('找不到左侧会话节点：')
                    }
                    break;
                }
            }
        }
    });
    observer.observe(document.body, {childList: true, subtree: true});

    // 监听会话列表切换
    const observePaneSide = (paneSideNode)=> {
        const observer = new MutationObserver(async (mutationsList) => {
            for (const mutation of mutationsList) {
                // 确保是 class 属性变化事件
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const targetNode = mutation.target;
                    // 检查 class 是否包含 selected
                    if (targetNode.classList.contains("css-19mzsj3-DivItemWrapper") || targetNode.classList.contains("css-u2mook-DivItemWrapper")) {
                        observeChatChanges();
                        debouncedSessionChange();
                    }
                }
            }
        });

        // 配置 MutationObserver，监听 class 属性变化
        const config = {attributes: true, subtree: true, attributeFilter: ["class"]};
        // 监听 pane-side 节点
        observer.observe(paneSideNode, config);
    }

    const observeChatChanges = () => {
        // 获取具有 data-e2e="chat-item" 属性的元素
        let chatItem = document.querySelector('[data-e2e="chat-item"]');
        // 获取父节点
        let chatContainer = chatItem.parentNode;
        if (chatContainer) {
            const observer = new MutationObserver((mutations) => {
                observer.disconnect(); // 暂时断开观察器以避免循环触发
                debouncedAddTranslateButtonToAllMessages()
                observer.observe(chatContainer, {
                    childList: true,
                    subtree: true,
                });
            });
            observer.observe(chatContainer, {
                childList: true,
                subtree: true,
            });
        }
    };

    const addTranslateButtonToAllMessages = async () => {
        const messageElements = document.querySelectorAll('[data-e2e="chat-item"]');
        for (let i = messageElements.length - 1; i >= 0; i--) {
            const msgSpan = messageElements[i].querySelector('p');
            const img = messageElements[i].querySelector('img')
            if (msgSpan && img) {
                const flag = msgSpan.hasAttribute('data-translated');
                if (flag === false) {
                    //添加属性表示已经处理过翻译
                    msgSpan.setAttribute('data-translated', 'yes');
                    await createTranslateButtonForMessage(msgSpan);
                }
            }
        }
    };
    //为每条消息创建翻译按钮
    const createTranslateButtonForMessage = async ( msgSpan) => {
        let text = msgSpan?.childNodes[0].textContent;
        if (!text) return;
        const translateDiv = document.createElement("div");
        translateDiv.style.cssText = `
        min-height: 20px;
        justify-content: space-between;
        margin-top: 5px;
        border-top:1px dashed gray;
        color:var(--secondary);
        display: flex;`;
        const leftDiv = document.createElement("div");
        leftDiv.style.cssText = `
            min-height: 20px;
            font-size:13px;
            filter: opacity(70%);
            color: green`;
        const rightDiv = document.createElement("div");
        rightDiv.style.cssText = `
            cursor: pointer;
            width: 20px;
            height: 20px;
            user-select: none;
            margin-left:5px;
        `;
        rightDiv.textContent = '🔄'
        rightDiv.addEventListener('click', async (e) => {
            let text = msgSpan?.childNodes[0].textContent;
            text = text.replace(/\n/g, '<br>');
            leftDiv.style.color = 'green';
            leftDiv.textContent = `翻译中...`;
            rightDiv.style.display = 'none';
            //发送请求获取翻译结果
            const route = trcConfig.translateRoute;
            const from = trcConfig.receiveSourceLanguage;
            const to = trcConfig.receiveTargetLanguage;
            const mode = trcConfig.mode;
            const res = await ipc.translateText({route: route, text: text,from:from, to: to,refresh:'true',mode:mode});
            if (res.status) {
                leftDiv.innerHTML = res.data;
                rightDiv.style.display = '';
            }else {
                leftDiv.style.color = 'red';
                leftDiv.textContent = `${res.message}`;
                rightDiv.style.display = '';
            }
        });
        // 组装结构
        translateDiv.appendChild(leftDiv);
        translateDiv.appendChild(rightDiv);
        // 插入到消息元素右侧
        msgSpan.appendChild(translateDiv);

        const receiveTranslateStatus = trcConfig.receiveTranslateStatus;
        if (receiveTranslateStatus === 'true') {
            let text = msgSpan?.childNodes[0].textContent;
            text = text.replace(/\n/g, '<br>');
            leftDiv.style.color = 'green';
            leftDiv.textContent = `翻译中...`;
            rightDiv.style.display = 'none';
            //发送请求获取翻译结果
            const route = trcConfig.translateRoute;
            const from = trcConfig.receiveSourceLanguage;
            const to = trcConfig.receiveTargetLanguage;
            const mode = trcConfig.mode;
            const res = await ipc.translateText({route: route, text: text,from:from, to: to,mode:mode});
            if (res.status) {
                leftDiv.innerHTML = res.data;
                rightDiv.style.display = '';
            }else {
                leftDiv.style.color = 'red';
                leftDiv.textContent = `${res.message}`;
                rightDiv.style.display = '';
            }
        }
    };
    const debouncedAddTranslateButtonToAllMessages = debounce(addTranslateButtonToAllMessages,200);
}
function debounce (func, delay)  {
    let timeout;
    return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), delay);
    };
};
monitorMainNode()

//快捷回复相关部分
function inputMsg(translation) {
    const editableDiv = document.querySelector("div.public-DraftStyleDefault-block")
    // const editableDiv = document.querySelector('div[contenteditable=true]');
    // 1. 检测操作系统
    const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
    // 2. 聚焦输入框
    editableDiv.focus();

    // 3. 模拟 Ctrl+A/Command+A
    const selectAll = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: !isMac,
        metaKey: isMac,
        bubbles: true
    });
    editableDiv.dispatchEvent(selectAll);
    // 4. 模拟退格键
    const backspace = new KeyboardEvent('keydown', {
        key: 'Backspace',
        code: 'Backspace',
        bubbles: true
    });
    editableDiv.dispatchEvent(backspace);

    // 查找富文本输入框
    let richTextInput = editableDiv.querySelector("span[data-text=true]")
    let cloneSpan = editableDiv.querySelector('span #myCloneSpan')

    if (!richTextInput && !cloneSpan) {
        const parentSpan = editableDiv.querySelector('span');
        cloneSpan = parentSpan.cloneNode(true); // 包含所有子节点
        // 分开属性名和值作为两个参数
        cloneSpan.setAttribute('data-text', 'true');
        cloneSpan.id='myCloneSpan'
        parentSpan.appendChild(cloneSpan)
        richTextInput = cloneSpan;
    }

    richTextInput.innerText = translation;
    // 7. 触发输入事件
    const inputEvent = new InputEvent('input', {
        bubbles: true,
        cancelable: true,
        inputType: 'insertText',
        data: translation
    });
    richTextInput.dispatchEvent(inputEvent);
    if (cloneSpan) cloneSpan.remove()
}
const quickReply = async (args)=>{
    await updateConfigInfo();
    const {type,text} = args;
    if (type === 'input') {
        await inputMsg(text);
    }
    if (type === 'send') {
        await inputMsg(text);
        let sendButton = document.querySelector("svg[data-e2e=message-send]");
        if (sendButton) {
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            });
            event.isSimulated = true; // 标记为模拟事件
            sendButton.dispatchEvent(event);
            const cloneSpan = document.querySelector('span #myCloneSpan')
            if (cloneSpan) cloneSpan.remove();
        }
    }
}

