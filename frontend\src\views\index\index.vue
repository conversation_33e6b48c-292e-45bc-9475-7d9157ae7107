<template>
  <div class="w-full h-full flex flex-col">
    <!--    头部区域-->
    <div class="w-full p-l4.5 h-12.5 bg-white flex items-center shadow-sm">Hi 欢迎来使用A2C，助力企业高效增长！</div>
    <!-- 主体区域-->
    <main class="w-full flex-1 bg-#e8e9eb p-x p-y4.5 overflow-hidden">
      <div class="flex w-full">
        <!-- 左侧区域 -->
        <div class="flex-1 min-w-0 mr-4">
          <div class="flex h-45 items-center justify-center">
            <div class="flex-1 mr h-full overflow-hidden">
              <!-- 轮播图 -->
              <carousel-image />
            </div>
            <div class="w-81 h-full bg-white">
              <translation-package />
            </div>
          </div>
          <div class="w-full h-56 bg-white mt5">
            <common-functions />
          </div>
        </div>
        <!-- 右侧区域 -->
        <div class="w-87.5 flex-shrink-0">
          <div class="w-full h-61 bg-white">
            <user-info />
          </div>
          <div class="w-full h-41 mt bg-white">
            <contact-us />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
import CarouselImage from '@/views/index/components/carousel-image.vue';
import CommonFunctions from '@/views/index/components/common-functions.vue';
import TranslationPackage from '@/views/index/components/translation-package.vue';
import UserInfo from '@/views/index/components/user-info.vue';
import ContactUs from '@/views/index/components/contact-us.vue';
</script>

<style lang="scss" scoped></style>
