<template>
  <div id="app">
    <div>
      <router-view/>
    </div>
    <update/>
  </div>
</template>
<script setup>
import {onMounted, ref, watch} from 'vue';
import {Close, FullScreen, Minus} from '@element-plus/icons-vue';
import {ipc} from '@/utils/ipcRenderer';
import {ipcApiRoute} from '@/api';
import {useMenuStore} from '@/stores/menuStore';
import {useUserInfoStore} from '@/stores/modules/userInfo';
import update from '@/components/update/index.vue';

const menuStore = useMenuStore();
const userStore = useUserInfoStore();
const barTitle = ref('');
const appPlatform = ref('');

onMounted(async () => {
  const loadingElement = document.getElementById('loadingPage');
  if (loadingElement) {
    loadingElement.remove();
  }
  const res = await ipc.invoke(ipcApiRoute.getSystemInfo, {});
  const {name, platform, version} = res;
  barTitle.value = name + ` v${version}`;
  appPlatform.value = platform;
  
  // 初始化用户信息（如果已登录则重新获取用户信息）
  await userStore.initUserInfo();
});

const winControl = async (action) => {
  await ipc.send('window-control', {action});
};

onMounted(async () => {
  /*  const res = await ipc.invoke(ipcApiRoute.getRouteList, {});
    console.log(res);
    if (res.status) {
      menuStore.setTranslationRoute(res.data);
    }*/
});
import {useDark} from '@vueuse/core';

const isDark = useDark();
// 监听 isDark 的变化
watch(
    isDark,
    (newVal) => {
      if (newVal === true) {
        ipc.invoke('theme-change', {theme: 'dark'});
      }
      if (newVal === false) {
        ipc.invoke('theme-change', {theme: 'light'});
      }
    },
    {immediate: true}
);
</script>
<style lang="scss">
.header {
  display: flex;
  -webkit-app-region: drag;
  height: 30px;
  /* 三等分核心代码 */
  .left,
  .center,
  .right {
    height: 30px;
    flex: 1; /* 关键属性：均分剩余空间 */
    display: flex; // 必须声明为 flex 容器
    align-items: center; // 垂直居中（核心）
    min-width: 0; /* 防止内容溢出破坏布局 */
  }

  /* 可选：内容对齐 */
  .left {
    justify-content: flex-start; /* 左对齐 */
  }

  .center {
    user-select: none; /* 禁止文本选择 */
    font-size: 12px;
    font-weight: bold;
    justify-content: center; /* 居中对齐 */
  }

  .right {
    margin-right: 5px;
    justify-content: flex-end; // 水平靠右
    gap: 5px; // 按钮间距（可选）
    .el-button {
      -webkit-app-region: no-drag;
      margin-left: 0;
      height: 20px;
      width: 20px;
    }
  }
}
</style>
