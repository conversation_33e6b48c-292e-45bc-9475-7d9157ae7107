<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import { debounce } from 'lodash-es';
// 获取目标元素的引用
const targetRef = ref(null);
let resizeObserver = null;
let currentRect = null; // 缓存上一次的位置信息

const locationInfo = ref({ x: 0, y: 0, height: 0, width: 0 });
// 自定义逻辑函数（根据需求修改）
const handleChange = debounce(async (width, height, rect) => {
  // 这里执行你的自定义逻辑
  locationInfo.value.x = rect.left + window.scrollX;
  locationInfo.value.y = rect.top + window.scrollY;
  locationInfo.value.width = width;
  locationInfo.value.height = height - 10;
  await ipc.invoke(ipcApiRoute.setWindowLocation, {
    x: locationInfo.value.x,
    y: locationInfo.value.y,
    width: locationInfo.value.width,
    height: locationInfo.value.height
  });
}, 50);

// 初始化观察者
const initObserver = () => {
  resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      const { width, height } = entry.contentRect;
      const newRect = targetRef.value.getBoundingClientRect();

      // 检测位置变化（比较上一次位置）
      const isPositionChanged = !currentRect || newRect.x !== currentRect.x || newRect.y !== currentRect.y;

      if (isPositionChanged || width !== currentRect?.width || height !== currentRect?.height) {
        currentRect = newRect;
        handleChange(width, height, newRect);
      }
    }
  });

  if (targetRef.value) {
    resizeObserver.observe(targetRef.value);
    currentRect = targetRef.value.getBoundingClientRect(); // 初始位置
  }
};

// 生命周期钩子
onMounted(initObserver);
onBeforeUnmount(() => {
  if (resizeObserver) resizeObserver.disconnect();
});
</script>

<template>
  <div ref="targetRef" class="un-known">
    <!-- 该组件用户显示加载的 webContentView窗口 -->
  </div>
</template>

<style scoped lang="scss">
.un-known {
  height: calc(100vh - 40px);
  width: 100%;
}
</style>
