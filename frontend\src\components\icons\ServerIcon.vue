<template>
  <svg t="1740503946498" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="743" :width="size" :height="size">
    <path
      d="M864 138.666667v768h-704v-768h704z m-64 533.333333h-576v170.666667h576v-170.666667zM704 725.333333v64h-128v-64h128z m96-288h-576v170.666667h576v-170.666667zM704 490.666667v64h-128v-64h128z m96-288h-576v170.666666h576v-170.666666zM704 256v64h-128v-64h128z"
      :fill="color"
      p-id="744"
    ></path>
  </svg>
</template>

<script setup>
defineProps({
  size: [Number, String],
  color: {
    type: String,
    default: 'currentColor' // 默认继承父级颜色
  }
});
</script>
