'use strict';

const { logger } = require('ee-core/log');
const os = require('os')
const { app, BrowserWindow } = require('electron')
const {Translate} = require('@google-cloud/translate').v2;
const CryptoJS = require("crypto-js");
const {post, get} = require("axios");
const VolcEngineSDK = require("volcengine-sdk");
const {getTimeStr} = require("../utils/CommonUtils");
const { ApiInfo, ServiceInfo, Credentials, API, Request } = VolcEngineSDK;

class TranslateService {

  async getConfigInfo(args,event) {
    let {platform,userId,partitionId} = args;
    if (!platform?.trim() && !userId?.trim()) {
      return {
        status: false,
        message: '参数不能同时为空，请至少填写一个有效参数'
      }
    }
    if (partitionId) {
      //获取窗口对象并查询是否有userid
      const view = app.viewsMap.get(partitionId)
      if (view && !view.webContents.isDestroyed()) {
        const nUserId = await view.webContents.executeJavaScript('getCurrentUserId()')
        if (nUserId?.trim()) {
          let configById = await app.sdb.selectOne('translate_config',{userId:nUserId})
          if (configById?.friendTranslateStatus && configById.friendTranslateStatus === 'true') {
            configById.showAloneBtn = 'true'
            return {status:true,message:'查询成功',data:configById}
          }else {
            let data = await app.sdb.selectOne('translate_config',{platform:platform})
            if (data) {
              data.showAloneBtn = 'true'
              return {status:true,message:'查询成功',data:data}
            }
          }
        }else {
          let data = await app.sdb.selectOne('translate_config',{platform:platform})
          if (data) {
            data.showAloneBtn = 'false'
            return {status:true,message:'查询成功',data:data}
          }
        }
      }
    }
    const configByPlatform = await app.sdb.selectOne('translate_config',{platform:platform})
    const configById = await app.sdb.selectOne('translate_config',{userId:userId})
    if (!configByPlatform && platform?.trim()) {
      //初始化平台翻译配置信息
      const initialData = {
        userId: "",
        platform: platform,
        receiveTranslateStatus: 'false',
        translateRoute:'youDao',
        receiveSourceLanguage: "auto",
        receiveTargetLanguage: "zh-CN",
        sendTranslateStatus: "true",
        sendSourceLanguage: "auto",
        sendTargetLanguage: "en",
        friendTranslateStatus: "false",
        showAloneBtn:'false',
        chineseDetectionStatus: "false",
        translatePreview: "true"
      }
      await app.sdb.insert('translate_config', initialData);
    }
    if (!configById && userId?.trim()) {
      //初始化平台翻译配置信息
      const initialData = {
        userId: userId,
        platform: '',
        translateRoute:'youDao',
        receiveTranslateStatus: 'false',
        receiveSourceLanguage: "auto",
        receiveTargetLanguage: "zh-CN",
        sendTranslateStatus: "true",
        sendSourceLanguage: "auto",
        sendTargetLanguage: "en",
        friendTranslateStatus: "false",
        showAloneBtn:'true',
        chineseDetectionStatus: "false",
        translatePreview: "true"
      }
      await app.sdb.insert('translate_config', initialData);
    }
    if (configById?.friendTranslateStatus && configById.friendTranslateStatus === 'true') {
      configById.showAloneBtn = 'true'
      return {status:true,message:'查询成功',data:configById}
    }else {
      const data = await app.sdb.selectOne('translate_config',{platform:platform})
      if (userId !== null && userId !== '') {
        data.showAloneBtn='true';
      }else {
        data.showAloneBtn='false';
      }
      return {status:true,message:'查询成功',data:data}
    }
  }
  // 后端服务方法
  async updateTranslateConfig(args,event) {
    // 参数解构
    const { key, value, id ,partitionId} = args;
    // 参数校验
    if (!id) throw new Error('缺少必要参数：id');
    if (!key) throw new Error('缺少必要参数：key');
    if (typeof value === 'undefined') return;
    try {
      // 构建更新对象（使用动态属性名）
      const updateData = {
        [key]: value
      };
      // 执行更新
      const changes = await app.sdb.update(
          'translate_config',
          updateData,
          { id:id }
      );
      // 返回最新配置（可选）
      const updatedConfig = await app.sdb.selectOne('translate_config', { id:id });
      if (partitionId) await this._routeUpdateNotify(partitionId)
      return {
        success: true,
        data: updatedConfig
      };
    } catch (error) {
      console.error('配置更新失败:', error);
      throw new Error(`配置更新失败: ${error.message}`);
    }
  }

  async changeAloneStatus(args,event) {
    const { status, partitionId } = args;
    if (!status?.trim() && !partitionId?.trim()) {
      return {
        status: false,
        message: '参数传递错误'
      }
    }
    //获取窗口对象并查询是否有userid
    const view = app.viewsMap.get(partitionId)
    if (view && !view.webContents.isDestroyed()) {
      const nUserId = await view.webContents.executeJavaScript('getCurrentUserId()')
      if (nUserId?.trim()) {
        const configById = await app.sdb.selectOne('translate_config',{userId:nUserId})
        if (configById) {
          //更新数据
          const count = await app.sdb.update('translate_config', {friendTranslateStatus:status},{id:configById.id})
          logger.info('状态修改成功：',args)
          if (partitionId) await this._routeUpdateNotify(partitionId)
          return {status:true,message:'数据更新成功'}
        }
      }
    }
    return {status:false,message:'配置不存在或窗口被关闭'}
  }

  async getLanguageList(args,event) {
    const list = await app.sdb.select('language_list',{})
    if (list) return {status:true,message:'查询成功',data:list}
    return {status:true,message:'查询成功',data:[]}
  }

  async addLanguage(args, event) {
    const { code, zhName, enName, youDao, baidu, huoShan, xiaoNiu, google, timestamp } = args;
    // 参数验证
    if (!code || !zhName) {
      return { status: false, message: '缺少必要参数，编码和名称为必填项' };
    }
    // 检查编码是否已存在
    const info = await app.sdb.selectOne('language_list', { code: code });
    if (info) {
      return { status: false, message: '当前编码已存在，请使用不同的编码' };
    }
    // 准备插入的数据
    const rows = { code: code, zhName: zhName };
    // 动态添加可选字段
    if (enName) rows['enName'] = enName;
    if (zhName) rows['zhName'] = zhName;
    if (timestamp) rows['timestamp'] = timestamp;
    if (youDao !== undefined) rows['youDao'] = youDao;
    if (baidu !== undefined) rows['baidu'] = baidu;
    if (huoShan !== undefined) rows['huoShan'] = huoShan;
    if (xiaoNiu !== undefined) rows['xiaoNiu'] = xiaoNiu;
    if (google !== undefined) rows['google'] = google;

    // 执行插入
    try {
      const id = await app.sdb.insert('language_list', rows);

      if (!id) {
        return { status: false, message: '数据写入失败，请稍后重试' };
      }

      // 查询插入后的数据
      const data = await app.sdb.selectOne('language_list', { id: id });

      return { status: true, message: '语言配置添加成功', data: data };
    } catch (error) {
      return { status: false, message: `添加失败，系统错误：${error.message}` };
    }
  }
  async deleteLanguage(args,event) {
    const {id} = args;
    if (!id) return {status:false,message:'id不能为空'}
    const count = await app.sdb.delete('language_list',{id:id})
    if (count >0) return {status:true,message:`成功删除${count}条数据`}
    return {status:false,message:`没有查询到这条数据`}
  }
  async editLanguage(args, event) {
    const { id, zhName, enName, code, youDao, baidu, huoShan, xiaoNiu, google } = args;

    // 参数验证
    if (!id || !zhName || !code) {
      return { status: false, message: '参数缺失，请检查 ID、名称和code。' };
    }
    // 创建要更新的字段对象
    const rows = { code, zhName, enName, youDao, baidu, huoShan, xiaoNiu, google };
    // 过滤掉值为空的字段，避免更新无效字段
    Object.keys(rows).forEach(key => {
      if (rows[key] === undefined || rows[key] === null) {
        delete rows[key];
      }
    });
    // 执行更新
    try {
      const count = await app.sdb.update('language_list', rows, { id });

      // 检查更新结果
      if (count > 0) {
        return { status: true, message: `语言配置更新成功` };
      } else {
        return { status: false, message: `没有找到对应的语言配置，更新失败。` };
      }
    } catch (error) {
      return { status: false, message: `更新失败，系统错误：${error.message}` };
    }
  }

  async addTranslateRoute(args,event) {
    const {name,zhName,enName,otherArgs} = args;
    const info = await app.sdb.selectOne('translate_route',{name:name})
    if (info) return;
    const rows = {name:name};
    if (enName) rows['enName'] = enName;
    if (zhName) rows['zhName'] = zhName;
    if (otherArgs) rows['otherArgs'] = otherArgs;
    await app.sdb.insert('translate_route',rows)
  }
  async editTranslateRoute(args,event) {
    const {name,dataJson} = args;
    const config = await app.sdb.selectOne('translate_route',{name:name})
    if (config) {
     const count = await app.sdb.update('translate_route',{otherArgs:dataJson},{name:name})
      if (count > 0) return {status:true,message:'修改成功'}
      return {status:false,message:'修改失败'}
    }
  }

  async getRouteConfig(args, event) {
    const { name } = args;
    const config = await app.sdb.selectOne('translate_route', { name: name });
    if (config) {
      try {
        const dataJson = JSON.parse(config.otherArgs); // 将 JSON 字符串转换为对象
        return {status:true,message:'查询成功',data:dataJson};
      } catch (error) {
        console.error('JSON 解析失败:', error);
        return {status:false,message:`解析参数失败：%${error.message}`};
      }
    }
    return {status:false,message:`找不到配置信息！`};
  }
  async getRouteList(args, event) {
    const routeList = await app.sdb.select('translate_route', {});
    if (routeList) {
      return {status:true,message:'查询成功',data:routeList};
    }
    return {status:false,message:`暂无翻译线路！`,data:[]};
  }

  async testRoute(args, event) {
    const { name } = args;
    const config = await app.sdb.selectOne('translate_route', { name: name });

    if (config) {
      try {
        const args = {
          route:config.name,
          text:`你好！${config.zhName}`,
          from:'auto',
          to:'en',
          mode:'local'
        }
        return  await this.translateText(args)

      } catch (error) {
        return {status:false,message:`翻译失败！${error.message}`};
      }
    } return {status:false,message:`找不到配置信息！`};
  }


  async translateText(args, event) {
    const { route, text, from, to ,partitionId,isFilter,mode,sourceTo} = args;
    const routeMap = {
      google: this._googleTranslateText,
      baidu: this._baiduTranslateText,
      youDao: this._youDaoTranslateText,
      huoShan: this._huoShanTranslateText,
      xiaoNiu: this._xiaoNiuTranslateText
    };

    if (isFilter === 'false') {
      //查询是否存在缓存
      const cache = await app.sdb.selectOne('translate_cache',{partitionId:partitionId,toCode:to,text:text});
      if (cache) {
        return { status: true, message: '翻译成功', data: cache.translateText };
      }
    }
    // logger.info('文本翻译：', args);
    try {
      if (mode === 'cloud') {
        const authInfo = app.authInfo;
        const url = app.baseUrl + '/translate_api';
        if (!authInfo) throw new Error('用户信息不存在！')
        const {userName,machineCode,authKey} = authInfo;
        const data = {
          translation_service: route,
          username: userName,
          source_lang:from,
          target_lang:sourceTo,
          text:text,
          key:authKey,
          device:machineCode
        }
        const result = await post(url,data)
        if (result?.data && isFilter === 'false') {
          //写入翻译消息缓存表
          await app.sdb.insert('translate_cache', {route:route,text:text,translateText:result.data.translate_text,fromCode:from,toCode:to,partitionId:partitionId,timestamp:getTimeStr()});
        }
        return { status: true, message: '翻译成功', data: result.data.translate_text };
      }
      if (mode === 'local') {
        if (!routeMap[route]) {
          throw new Error(`不支持的翻译服务: ${route}`);
        }
        // 调用指定的翻译函数
        const result = await routeMap[route].call(this, text, to,route);
        // logger.info(result);
        if (result && isFilter === 'false') {
          //写入翻译消息缓存表
          await app.sdb.insert('translate_cache', {route:route,text:text,translateText:result,fromCode:from,toCode:to,partitionId:partitionId,timestamp:getTimeStr()});
        }
        return { status: true, message: '翻译成功', data: result };
      }
    } catch (err) {
      const msg = err.response?.data?.error || err.message;
      logger.error('翻译请求失败：',msg)
      return { status: false, message: '翻译失败!请重试或更换其它翻译线路!' };
    }
  }

  //翻译线路更改通知
  async _routeUpdateNotify (partitionId) {
    if (!partitionId) return;
    const view = app.viewsMap.get(partitionId);
    if (view && !view.webContents.isDestroyed()) {
      await view.webContents.executeJavaScript('updateConfigInfo()')
    }
  }

  async _googleTranslateText(text,target,route) {
    const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=${target}&dt=t&q=${encodeURIComponent(text)}`;

    const response = await get(url);
    const data = await response.data;

    if (data && data[0]) {
      // 合并所有翻译段落
      const translation = data[0]
          .filter(item => item && item[0])  // 过滤掉空值
          .map(item => item[0])             // 提取翻译文本
          .join('\n');                      // 用换行符连接

      return translation;
    } else {
      throw new Error('Translation result format error');
    }
  }

  async _baiduTranslateText(text,target,route) {
    const config = await app.sdb.selectOne('translate_route',{name:route})
    if (!config) {
      throw new Error('找不到当前翻译线路配置！')
    }
    try {
      const configData = JSON.parse(config.otherArgs); // 将 JSON 字符串转换为对象
      const appid = configData?.appId;
      const key = configData?.apiKey;
      const url = configData?.apiUrl;
      const salt = Date.now();  // 生成随机数
      const sign = CryptoJS.MD5(appid + text + salt + key).toString();  // 计算 MD5 签名
      const response = await get(url, {
        params: {
          q: text,
          from: 'auto',
          to: target,
          appid: appid,
          salt: salt,
          sign: sign
        }
      });
      const data = response.data;
      if (data.trans_result) {
        return data.trans_result.map(item => item.dst).join("\n");
      } else {
        throw new Error(`翻译失败:${data?.error_code} : ${data?.error_msg}`)
      }
    } catch (error) {
      throw new Error(`${error.message}`)
    }
  }
  async _youDaoTranslateText(text,target,route) {
    const config = await app.sdb.selectOne('translate_route',{name:route})
    if (!config) {
      throw new Error('找不到当前翻译线路配置！')
    }
    try {
      const configData = JSON.parse(config.otherArgs); // 将 JSON 字符串转换为对象
      const appKey = configData?.appId;
      const appSecret = configData?.apiKey;
      const url = configData?.apiUrl;
      // 生成签名所需参数
      const salt = Date.now().toString();
      const curtime = Math.floor(Date.now() / 1000).toString();
      // 处理长文本（截取规则）
      const truncate = (q) => {
        if (q.length <= 20) return q;
        return q.substring(0, 10) + q.length + q.substring(q.length - 10);
      };
      // 生成签名
      const signStr = appKey + truncate(text) + salt + curtime + appSecret;
      const sign = CryptoJS.SHA256(signStr).toString(CryptoJS.enc.Hex);
      // 构造请求参数
      const params = new URLSearchParams();
      params.append('q', text);
      params.append('from', 'auto');
      params.append('to', target);
      params.append('appKey', appKey);
      params.append('salt', salt);
      params.append('sign', sign);
      params.append('signType', 'v3');
      params.append('curtime', curtime);
      params.append('strict', "true");
      // 发送请求
      const response = await post(
          url,
          params,
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
      );
      // 错误处理
      if (response.data.errorCode !== '0') {
        throw new Error(`翻译失败，错误码：${response.data.errorCode}`);
      }
      // 返回翻译结果
      return response.data.translation[0];
    } catch (error) {
      throw error;
    }
  }

  async _huoShanTranslateText(text, target, route) {
    const config = await app.sdb.selectOne('translate_route',{name:route})
    if (!config) {
      throw new Error('找不到当前翻译线路配置！')
    }
    const configData = JSON.parse(config.otherArgs);
    const AK = configData.apiKey
    const SK = configData.secretKey
    // 翻译目标语言、翻译文本列表
    const toLang = target;
    const textList = [text];
    // api凭证
    const credentials = new Credentials(AK, SK, 'translate', 'cn-north-1');
    // 设置请求的 header、query、body
    const header = new Request.Header({
      'Content-Type': 'application/json'
    });
    const query = new Request.Query({
      'Action': 'TranslateText',
      'Version': '2020-06-01'
    });
    const body = new Request.Body({
      'TargetLanguage': toLang,
      'TextList': textList
    });

    // 设置 service、api信息
    const serviceInfo = new ServiceInfo(
        'open.volcengineapi.com',
        header,
        credentials
    );
    const apiInfo = new ApiInfo('POST', '/', query, body);

    // 生成 API
    const api = API(serviceInfo, apiInfo);
    const res = await post(api.url, api.params, api.config)
    const translationList = res.data?.TranslationList;
    const responseMetadata = res.data?.ResponseMetadata;
    // logger.info(translationList);
    // logger.info(responseMetadata);
    if (translationList && translationList.length > 0) {
      // 提取第一个翻译结果的 Translation 字段
      return  translationList[0].Translation;
    } else {
      throw new Error(`翻译出错：${responseMetadata.Error.CodeN} : ${responseMetadata.Error.Code}`);
    }

  }

  async _xiaoNiuTranslateText(text,target,route) {
    const config = await app.sdb.selectOne('translate_route',{name:route})
    if (!config) {
      throw new Error('找不到当前翻译线路配置！')
    }
    const configData = JSON.parse(config.otherArgs);
    const apikey = configData?.apiKey;
    const url = configData?.apiUrl;

    // 构造请求参数
    const params = {
      from: 'auto',
      to: target,
      apikey: apikey,
      src_text: text
    };

    // 使用 axios 发送 GET 请求
    const response = await get(url, { params });
    // 如果返回数据中包含翻译结果，则返回翻译结果
    if (response.data && response.data.tgt_text) {
      return response.data.tgt_text;
    }else {
      throw new Error(`翻译出错：${response.data.error_code} ${response.data.error_msg}`)
    }
  }

  // 清理会话翻译缓存
  async clearSessionCache(args, event) {
    try {
      const { partitionId } = args;

      if (!partitionId) {
        return {
          status: false,
          message: '会话ID不能为空'
        };
      }

      // 删除指定会话的翻译缓存
      const deletedCount = await app.sdb.delete('translate_cache', { partitionId: partitionId });

      logger.info(`清理会话 ${partitionId} 的翻译缓存，删除了 ${deletedCount} 条记录`);

      return {
        status: true,
        message: `成功清理 ${deletedCount} 条翻译缓存`,
        data: {
          partitionId: partitionId,
          deletedCount: deletedCount
        }
      };
    } catch (error) {
      logger.error('清理会话翻译缓存失败:', error);
      return {
        status: false,
        message: '清理翻译缓存失败'
      };
    }
  }

}
TranslateService.toString = () => '[class TranslateService]';

module.exports = {
  TranslateService,
  translateService: new TranslateService()
};
