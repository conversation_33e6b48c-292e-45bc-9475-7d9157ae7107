'use strict';

const { logger } = require('ee-core/log');
const {windowService} = require("../service/window");

/**
 * SystemController 获取系统基本信息
 * @class
 */
class WindowController {

    // 添加会话窗口
    async addSession(args,event) {
        return await windowService.addSession(args,event);
    }
    // 修改会话信息
    async editSession(args,event) {
        return await windowService.editSession(args,event);
    }
    // 启动会话
    async startSession(args,event) {
        return await windowService.startSession(args,event);
    }
    // 获取所有会话信息
    async getSessions(args,event) {
        return await windowService.getSessions(args,event);
    }
    // 根据分区id查询会话信息
    async getSessionByPartitionId(args,event) {
        return await windowService.getSessionByPartitionId(args,event);
    }
    // 设置会话窗口位置
    async setWindowLocation(args,event) {
        return await windowService.setWindowLocation(args,event);
    }
    //隐藏会话窗口
    async hiddenSession(args,event) {
        return await windowService.hiddenSession(args,event);
    }
    //显示会话窗口
    async showSession(args,event) {
        return await windowService.showSession(args,event);
    }
    //关闭会话窗口
    async closeSession(args,event) {
        return await windowService.closeSession(args,event);
    }
    //刷新会话
    async refreshSession(args,event) {
        return await windowService.refreshSession(args,event);
    }
    //删除会话窗口
    async deleteSession(args,event) {
        return await windowService.deleteSession(args,event);
    }
    //获取窗口代理配置信息
    async getProxyInfo(args,event) {
        return await windowService.getProxyInfo(args,event);
    }
    //修改窗口代理配置信息
    async editProxyInfo(args,event) {
        return await windowService.editProxyInfo(args,event);
    }
    //修改窗口代理配置信息(多属性一次性修改)
    async saveProxyInfo(args,event) {
        return await windowService.saveProxyInfo(args,event);
    }
    //打开当前会话控制台
    async openSessionDevTools(args,event) {
        return await windowService.openSessionDevTools(args,event);
    }
}
WindowController.toString = () => '[class WindowController]';

module.exports = WindowController;
