'use strict';
const { logger } = require('ee-core/log');
const { app, BrowserWindow } = require('electron')
class QuickReplyService {

  async getGroups(args,event) {
    const groups = await app.sdb.select('group_manage');
    for (let group of groups) {
      const records = await app.sdb.select('quick_reply_record',{groupId:group.id})
      group.contents = records;
      group.contentCount = records.length;
    }
    return {status:true,message:'查询成功',data:groups};
  }

  async getContentByGroupId(args,event) {
    const {groupId} = args;
    const records = await app.sdb.select('quick_reply_record',{groupId:groupId})
    return {status:true,message:'查询成功',data:records};
  }

  async addGroup(args,event) {
    const {name} = args;
    await app.sdb.insert('group_manage',{name:name})
    return {status:true,message:'添加成功'};
  }

  async editGroup(args,event) {
    const {name,id} = args;
    await app.sdb.update('group_manage',{name:name},{id:id})
    return {status:true,message:'修改成功'};
  }

  async deleteGroup(args,event) {
    const {id} = args;
    await app.sdb.delete('group_manage',{id:id})
    await app.sdb.delete('quick_reply_record',{groupId:id})
    return {status:true,message:'删除成功'};
  }

  async addReply(args,event) {
    const {remark,content,url,type,groupId} = args;
    if (!groupId) return {status:false,message:'分组id不能为空'};
    // 创建要更新的字段对象
    const rows = { remark,content,url,type,groupId };
    // 过滤掉值为空的字段，避免更新无效字段
    Object.keys(rows).forEach(key => {
      if (rows[key] === undefined || rows[key] === null) {
        delete rows[key];
      }
    });
    try {
      await app.sdb.insert('quick_reply_record',rows)
      return {status:true,message:'新增成功'}
    }catch(err) {
      return {status:false,message:`系统错误：${err.message}`};
    }
  }
  async editReply(args,event) {
    const { id, remark,content,url } = args;
    // 参数验证
    if (!id || !remark) {
      return { status: false, message: '参数缺失，请检查' };
    }
    // 创建要更新的字段对象
    const rows = { remark,content,url };
    // 过滤掉值为空的字段，避免更新无效字段
    Object.keys(rows).forEach(key => {
      if (rows[key] === undefined || rows[key] === null) {
        delete rows[key];
      }
    });
    // 执行更新
    try {
      await app.sdb.update('quick_reply_record', rows, { id });
      return { status: true, message: `更新成功` };
    } catch (error) {
      return { status: false, message: `系统错误：${error.message}` };
    }
  }
  async deleteReply(args,event) {
    const {id} = args;
    if (!id) return {status:false,message:'id不能为空'}
    await app.sdb.delete('quick_reply_record',{id:id})
    return {status:true,message:'删除成功'}
  }
  async deleteAllReply(args,event) {
    const {groupId} = args;
    if (!groupId) return {status:false,message:'分组id不能为空'}
    await app.sdb.delete('quick_reply_record',{groupId:groupId})
    return {status:true,message:'清空数据成功'}
  }

}
QuickReplyService.toString = () => '[class QuickReplyService]';

module.exports = {
  QuickReplyService,
  quickReplyService: new QuickReplyService()
};
