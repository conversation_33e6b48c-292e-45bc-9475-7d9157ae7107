<template>
  <div class="flex items-center mt-10px search-input">
    <el-input v-model="search" placeholder="备注、昵称、手机号">
      <template #prefix>
        <el-icon>
          <Search />
        </el-icon>
      </template>
    </el-input>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Search } from '@element-plus/icons-vue';

const search = ref('');
</script>

<style lang="scss" scoped>
.search-input {
  :deep(.el-input__wrapper) {
    background-color: #f0f2f5;
    border-radius: 0;
    border: 0;
    box-shadow: none;
  }
}
</style>
