<script setup>
import { <PERSON>, <PERSON> } from '@element-plus/icons-vue';
import { useDark } from '@vueuse/core';
import { computed } from 'vue';

// 定义props
const props = defineProps({
  size: {
    type: Number,
    default: 32
  }
});

const isDark = useDark();

// 计算样式
const switchStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`
}));

const iconSize = computed(() => ({
  fontSize: `${Math.max(props.size * 0.5625, 14)}px` // 保持图标大小为容器大小的0.5625倍，最小14px
}));
</script>

<template>
  <div class="theme-switch">
    <div class="switch-content" :style="switchStyle">
      <el-icon v-if="isDark" @click="isDark = false" :style="iconSize"><Sunny /></el-icon>
      <el-icon v-else @click="isDark = true" :style="iconSize"><Moon /></el-icon>
    </div>
  </div>
</template>

<style scoped>
.theme-switch {
  display: flex;
  align-items: center;
  padding: 0;
  transition: all 0.3s;
}

.switch-content {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: var(--el-fill-color-light);
  border-radius: 50%;
  transition: all 0.3s;
}

.switch-content:hover {
  transform: translateY(-1px);
  background: var(--el-fill-color);
}

:deep(.el-icon) {
  color: var(--el-text-color-regular);
}
</style>
