<template>
  <div class="huoshan-translate">
    <el-divider content-position="left">基础信息配置</el-divider>

    <div class="hs-content-input">
      <div class="content-left">
        <el-text>URL</el-text>
      </div>
      <div class="content-right">
        <el-input disabled v-model="config.apiUrl" placeholder="火山翻译API请求地址">
          <!-- 使用 append 插槽来添加按钮 -->
          <template #append>
            <el-button type="success" @click="handleTest" size="small">测试连接</el-button>
          </template>
        </el-input>
      </div>
    </div>

    <div class="hs-content-input">
      <div class="content-left">
        <el-text>apiKey</el-text>
      </div>
      <div class="content-right">
        <el-input v-model="config.apiKey" type="password" show-password placeholder="火山翻译授权密钥KEY"></el-input>
      </div>
    </div>

    <div class="hs-content-input">
      <div class="content-left">
        <el-text>secretKey</el-text>
      </div>
      <div class="content-right">
        <el-input v-model="config.secretKey" type="password" show-password placeholder="火山翻译授权私钥"></el-input>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import { ElMessage } from 'element-plus';

const props = defineProps({
  tabName: {
    type: String,
    required: true
  }
});

const config = ref({
  apiUrl: '',
  appId: '',
  apiKey: ''
});
const editConfig = async () => {
  const args = {
    name: props.tabName,
    dataJson: jsonConfig.value
  };
  await ipc.invoke(ipcApiRoute.editTranslateRoute, args);
};
const handleTest = async () => {
  const res = await ipc.invoke(ipcApiRoute.testRoute, { name: props.tabName });
  if (res.status) {
    ElMessage({
      message: `翻译成功：${res.data}`,
      type: 'success',
      offset: 40
    });
  } else {
    ElMessage({
      message: `翻译失败：${res.message}`,
      type: 'error',
      offset: 40
    });
  }
};
// 计算属性：将 config 转换成 JSON 格式
const jsonConfig = computed(() => {
  return JSON.stringify(config.value, null, 2); // 格式化 JSON 数据
});
onMounted(async () => {
  const res = await ipc.invoke(ipcApiRoute.getRouteConfig, { name: props.tabName });
  if (res.status) {
    config.value = res.data;
  }
});

// 监听config变化，在必要时调用editConfig
watch(
  config,
  async (newVal, oldVal) => {
    await editConfig();
  },
  { deep: true, immediate: false }
);
</script>

<style scoped lang="scss">
.huoshan-translate {
  height: 200px;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;

  .hs-content-input {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    :deep(.el-input__wrapper) {
      border-radius: 0;
    }
    .content-left {
      height: 50px;
      display: flex;
      width: 100px;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 1;
      align-items: center;
    }
  }
}
</style>
