import { createApp } from 'vue';
import App from './App.vue';
import './assets/global.scss';
import 'uno.css';
import components from './components/global';
import Router from './router/index';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
import './assets/element-theme.scss';
import { createPinia } from 'pinia';
import i18n from './i18n';
import './permission';
import './assets/iconfont/iconfont';
import './assets/iconfont/iconfont.css';
import 'virtual:svg-icons-register';

const pinia = createPinia();
const app = createApp(App);

// components
for (const i in components) {
  app.component(i, (components as any)[i]);
}
app.use(ElementPlus);
app.use(pinia);
app.use(i18n);
app.use(Router).mount('#app');
