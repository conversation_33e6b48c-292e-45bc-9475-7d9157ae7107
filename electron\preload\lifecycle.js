'use strict';

const {logger} = require('ee-core/log');
const {getConfig} = require('ee-core/config');
const {getMainWindow} = require('ee-core/electron');
const {app, shell, globalShortcut} = require("electron");
const Database = require('../utils/DatabaseUtils');
const {getTimeStr} = require("../utils/CommonUtils");

const baseUrl = 'http://127.10.10.1:8000/api'
const initializeDatabase = async () => {
    // 定义表结构
    const tables = {
        'parameter': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT', // 添加自增主键字段
                key: 'TEXT',          // 键
                value: 'TEXT',          // 值
            },
            constraints: []
        },
        'tg_sessions': {
            columns: {
                phoneNumber: 'TEXT',
                sessionStr: 'TEXT',
            },
            constraints: [
                'PRIMARY KEY(phoneNumber)',
            ]
        },
        'session_list': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                partitionId: 'TEXT',
                windowId: 'INTEGER',
                windowStatus: 'TEXT',
                platform: 'TEXT',
                onlineStatus: 'TEXT',
                createTime: 'TEXT',
                remarks: 'TEXT',
                webUrl: 'TEXT',
                avatarUrl: 'TEXT',
                userName: 'TEXT',
                nickName: 'TEXT',
                msgCount: 'INTEGER',
                userAgent: 'TEXT'
            },
            constraints: []
        },
        'translate_config': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                userId: 'TEXT',
                platform: 'TEXT',
                mode: 'TEXT DEFAULT "cloud"',//翻译模式 本地 local 云端 cloud
                translateRoute: 'TEXT',
                receiveTranslateStatus: 'TEXT',
                receiveSourceLanguage: 'TEXT',
                receiveTargetLanguage: 'TEXT',
                sendTranslateStatus: 'TEXT',
                sendSourceLanguage: 'TEXT',
                sendTargetLanguage: 'TEXT',
                friendTranslateStatus: 'TEXT',
                showAloneBtn: 'TEXT',
                chineseDetectionStatus: 'TEXT',
                translatePreview: 'TEXT',
            },
            constraints: []
        },
        'translate_route': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                name: 'TEXT',
                zhName: 'TEXT',
                enName: 'TEXT',
                otherArgs: 'TEXT',
            },
            constraints: []
        },
        'contact_info': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                userId: 'TEXT',
                platform: 'TEXT',
                phoneNumber: 'TEXT',
                nickName: 'TEXT',
                country: 'TEXT',
                gender: 'TEXT',
                gradeActivity: 'INTEGER',
                customLevel: 'INTEGER',
                remarks: 'TEXT',
            },
            constraints: []
        },
        'follow_record': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                userId: 'TEXT',
                platform: 'TEXT',
                content: 'TEXT',
                timestamp: 'TEXT',
            },
            constraints: []
        },
        'language_list': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                code: 'TEXT',
                zhName: 'TEXT',
                enName: 'TEXT',
                baidu: 'TEXT',
                youDao: 'TEXT',
                huoShan: 'TEXT',
                xiaoNiu: 'TEXT',
                google: 'TEXT',
                timestamp: 'TEXT',
            },
            constraints: []
        },
        'translate_cache': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                route: 'TEXT',
                text: 'TEXT',
                translateText: 'TEXT',
                fromCode: 'TEXT',
                toCode: 'TEXT',
                partitionId: 'TEXT',
                timestamp: 'TEXT',
            },
            constraints: []
        },
        'proxy_config': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                partitionId: 'TEXT',
                proxyStatus: 'TEXT',
                proxyType: 'TEXT',
                proxyIp: 'TEXT',
                proxyPort: 'TEXT',
                userVerifyStatus: 'TEXT',
                username: 'TEXT',
                password: 'TEXT',
            },
            constraints: []
        },
        'group_manage': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                name: 'TEXT',
            },
            constraints: []
        },
        'quick_reply_record': {
            columns: {
                id: 'INTEGER PRIMARY KEY AUTOINCREMENT',
                groupId: 'TEXT',
                type: 'TEXT',
                remark: 'TEXT',
                content: 'TEXT',
                url: 'TEXT',
            },
            constraints: []
        },

    };
    // 同步每个表的结构
    for (const [tableName, {columns, constraints}] of Object.entries(tables)) {
        await new Database().syncTableStructure(tableName, columns, constraints);
    }
    app.sdb = new Database();
}
const initializePlatform = async () => {
    const platforms = [
        {platform: 'Telegram', url: 'https://web.telegram.org/a/'},
        {platform: 'WhatsApp', url: 'https://web.whatsapp.com/'},
        {platform: 'TikTok', url: 'https://www.tiktok.com/messages?lang=zh-Hans'},
    ];
    app.platforms = platforms;
}
const initializeTableData = async () => {
    await app.sdb.update('session_list', {windowStatus: 'false', msgCount: 0, onlineStatus: 'false', windowId: 0}, {})
    // 初始化翻译线路
    const translationRoute = [
        {
            name: 'youDao',
            zhName: '有道翻译',
            enName: 'Youdao Translate',
            otherArgs: `{"apiUrl": "https://openapi.youdao.com/api","appId": "","apiKey": ""}`
        },
        {
            name: 'baidu',
            zhName: '百度翻译',
            enName: 'Baidu Translate',
            otherArgs: `{"apiUrl": "https://fanyi-api.baidu.com/api/trans/vip/translate","appId": "","apiKey": ""}`
        },
        {
            name: 'huoShan',
            zhName: '火山翻译',
            enName: 'VolcEngine Translate',
            otherArgs: `{"apiUrl": "https://translate.volcengineapi.com","apiKey": "","secretKey": ""}`
        },
        {
            name: 'xiaoNiu',
            zhName: '小牛翻译',
            enName: 'Niutrans',
            otherArgs: `{"apiUrl": "https://api.niutrans.com/NiuTransServer/translation","apiKey": ""}`
        },
        {
            name: 'google',
            zhName: '谷歌翻译',
            enName: 'Google Translate',
            otherArgs: `{"apiUrl": "https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto"}`
        },
    ]
    for (let item of translationRoute) {
        const args = {
            name: item.name,
            zhName: item.zhName,
            enName: item.enName,
            otherArgs: item.otherArgs
        };
        const route = await app.sdb.selectOne('translate_route', {name: item.name});
        if (!route) {
            // 初始化翻译线路
            await app.sdb.insert('translate_route', args)
        }
    }
    //初始化翻译编码信息
    // 插入中文（简体）
    const zh = await app.sdb.selectOne('language_list', {code: 'zh-CN'});
    if (!zh) {
        app.sdb.insert('language_list', {
            code: 'zh-CN',
            zhName: '简体中文',
            enName: 'Simplified Chinese',
            timestamp: getTimeStr(),
            baidu: 'zh',          // 百度：中文(zh)
            youDao: 'zh-CHS',     // 有道：中文(zh-CHS)
            huoShan: 'zh-Hans',   // 火山：中文(zh-Hans)
            xiaoNiu: 'zh',        // 小牛：中文(zh)
            google: 'zh-CN'       // 谷歌：中文(zh-CN)
        });
    }

// 插入英语
    const en = await app.sdb.selectOne('language_list', {code: 'en'});
    if (!en) {
        app.sdb.insert('language_list', {
            code: 'en',
            zhName: '英语',
            enName: 'English',
            timestamp: getTimeStr(),
            baidu: 'en',          // 百度
            youDao: 'en',         // 有道
            huoShan: 'en',        // 火山
            xiaoNiu: 'en',        // 小牛
            google: 'en'          // 谷歌
        });
    }

// 插入西班牙语
    const es = await app.sdb.selectOne('language_list', {code: 'es'});
    if (!es) {
        app.sdb.insert('language_list', {
            code: 'es',
            zhName: '西班牙语',
            enName: 'Spanish',
            timestamp: getTimeStr(),
            baidu: 'spa',         // 百度使用西班牙语缩写
            youDao: 'es',         // 有道
            huoShan: 'es',        // 火山
            xiaoNiu: 'es',        // 小牛
            google: 'es'          // 谷歌
        });
    }

// 插入法语
    const fr = await app.sdb.selectOne('language_list', {code: 'fr'});
    if (!fr) {
        app.sdb.insert('language_list', {
            code: 'fr',
            zhName: '法语',
            enName: 'French',
            timestamp: getTimeStr(),
            baidu: 'fra',         // 百度使用法语缩写
            youDao: 'fr',         // 有道
            huoShan: 'fr',        // 火山
            xiaoNiu: 'fr',        // 小牛
            google: 'fr'          // 谷歌
        });
    }

// 插入阿拉伯语
    const ar = await app.sdb.selectOne('language_list', {code: 'ar'});
    if (!ar) {
        app.sdb.insert('language_list', {
            code: 'ar',
            zhName: '阿拉伯语',
            enName: 'Arabic',
            timestamp: getTimeStr(),
            baidu: 'ara',         // 百度使用标准代码
            youDao: 'ar',         // 有道
            huoShan: 'ar',        // 火山
            xiaoNiu: 'ar',        // 小牛
            google: 'ar'          // 谷歌
        });
    }

// 插入葡萄牙语
    const pt = await app.sdb.selectOne('language_list', {code: 'pt'});
    if (!pt) {
        app.sdb.insert('language_list', {
            code: 'pt',
            zhName: '葡萄牙语',
            enName: 'Portuguese',
            timestamp: getTimeStr(),
            baidu: 'pt',          // 百度
            youDao: 'pt',         // 有道
            huoShan: 'pt',        // 火山
            xiaoNiu: 'pt',        // 小牛
            google: 'pt'          // 谷歌
        });
    }

// 插入俄语
    const ru = await app.sdb.selectOne('language_list', {code: 'ru'});
    if (!ru) {
        app.sdb.insert('language_list', {
            code: 'ru',
            zhName: '俄语',
            enName: 'Russian',
            timestamp: getTimeStr(),
            baidu: 'ru',          // 百度
            youDao: 'ru',         // 有道
            huoShan: 'ru',        // 火山
            xiaoNiu: 'ru',        // 小牛
            google: 'ru'          // 谷歌
        });
    }

// 插入德语
    const de = await app.sdb.selectOne('language_list', {code: 'de'});
    if (!de) {
        app.sdb.insert('language_list', {
            code: 'de',
            zhName: '德语',
            enName: 'German',
            timestamp: getTimeStr(),
            baidu: 'de',          // 百度
            youDao: 'de',         // 有道
            huoShan: 'de',        // 火山
            xiaoNiu: 'de',        // 小牛
            google: 'de'          // 谷歌
        });
    }

// 插入日语
    const ja = await app.sdb.selectOne('language_list', {code: 'ja'});
    if (!ja) {
        app.sdb.insert('language_list', {
            code: 'ja',
            zhName: '日语',
            enName: 'Japanese',
            timestamp: getTimeStr(),
            baidu: 'jp',          // 百度特殊代码
            youDao: 'ja',         // 有道
            huoShan: 'ja',        // 火山
            xiaoNiu: 'ja',        // 小牛
            google: 'ja'          // 谷歌
        });
    }

// 插入韩语
    const ko = await app.sdb.selectOne('language_list', {code: 'ko'});
    if (!ko) {
        app.sdb.insert('language_list', {
            code: 'ko',
            zhName: '韩语',
            enName: 'Korean',
            timestamp: getTimeStr(),
            baidu: 'kor',         // 百度使用韩语缩写
            youDao: 'ko',         // 有道
            huoShan: 'ko',        // 火山
            xiaoNiu: 'ko',        // 小牛
            google: 'ko'          // 谷歌
        });
    }

// 插入意大利语
    const it = await app.sdb.selectOne('language_list', {code: 'it'});
    if (!it) {
        app.sdb.insert('language_list', {
            code: 'it',
            zhName: '意大利语',
            enName: 'Italian',
            timestamp: getTimeStr(),
            baidu: 'it',          // 百度
            youDao: 'it',         // 有道
            huoShan: 'it',        // 火山
            xiaoNiu: 'it',        // 小牛
            google: 'it'          // 谷歌
        });
    }

// 插入荷兰语
    const nl = await app.sdb.selectOne('language_list', {code: 'nl'});
    if (!nl) {
        app.sdb.insert('language_list', {
            code: 'nl',
            zhName: '荷兰语',
            enName: 'Dutch',
            timestamp: getTimeStr(),
            baidu: 'nl',          // 百度
            youDao: 'nl',         // 有道
            huoShan: 'nl',        // 火山
            xiaoNiu: 'nl',        // 小牛
            google: 'nl'          // 谷歌
        });
    }

// 插入土耳其语
    const tr = await app.sdb.selectOne('language_list', {code: 'tr'});
    if (!tr) {
        app.sdb.insert('language_list', {
            code: 'tr',
            zhName: '土耳其语',
            enName: 'Turkish',
            timestamp: getTimeStr(),
            baidu: 'tr',          // 百度
            youDao: 'tr',         // 有道
            huoShan: 'tr',        // 火山
            xiaoNiu: 'tr',        // 小牛
            google: 'tr'          // 谷歌
        });
    }

// 插入越南语
    const vi = await app.sdb.selectOne('language_list', {code: 'vi'});
    if (!vi) {
        app.sdb.insert('language_list', {
            code: 'vi',
            zhName: '越南语',
            enName: 'Vietnamese',
            timestamp: getTimeStr(),
            baidu: 'vie',         // 百度使用越南语缩写
            youDao: 'vi',         // 有道
            huoShan: 'vi',        // 火山
            xiaoNiu: 'vi',        // 小牛
            google: 'vi'          // 谷歌
        });
    }

// 插入泰语
    const th = await app.sdb.selectOne('language_list', {code: 'th'});
    if (!th) {
        app.sdb.insert('language_list', {
            code: 'th',
            zhName: '泰语',
            enName: 'Thai',
            timestamp: getTimeStr(),
            baidu: 'th',          // 百度
            youDao: 'th',         // 有道
            huoShan: 'th',        // 火山
            xiaoNiu: 'th',        // 小牛
            google: 'th'          // 谷歌
        });
    }

// 插入波兰语
    const pl = await app.sdb.selectOne('language_list', {code: 'pl'});
    if (!pl) {
        app.sdb.insert('language_list', {
            code: 'pl',
            zhName: '波兰语',
            enName: 'Polish',
            timestamp: getTimeStr(),
            baidu: 'pl',          // 百度
            youDao: 'pl',         // 有道
            huoShan: 'pl',        // 火山
            xiaoNiu: 'pl',        // 小牛
            google: 'pl'          // 谷歌
        });
    }

// 插入印尼语
    const id = await app.sdb.selectOne('language_list', {code: 'id'});
    if (!id) {
        app.sdb.insert('language_list', {
            code: 'id',
            zhName: '印尼语',
            enName: 'Indonesian',
            timestamp: getTimeStr(),
            baidu: 'id',          // 百度
            youDao: 'id',         // 有道
            huoShan: 'id',        // 火山
            xiaoNiu: 'id',        // 小牛
            google: 'id'          // 谷歌
        });
    }

// 插入乌克兰语
    const uk = await app.sdb.selectOne('language_list', {code: 'uk'});
    if (!uk) {
        app.sdb.insert('language_list', {
            code: 'uk',
            zhName: '乌克兰语',
            enName: 'Ukrainian',
            timestamp: getTimeStr(),
            baidu: 'ukr',         // 百度使用乌克兰语缩写
            youDao: 'uk',         // 有道
            huoShan: 'uk',        // 火山
            xiaoNiu: 'uk',        // 小牛
            google: 'uk'          // 谷歌
        });
    }

// 插入希腊语
    const el = await app.sdb.selectOne('language_list', {code: 'el'});
    if (!el) {
        app.sdb.insert('language_list', {
            code: 'el',
            zhName: '希腊语',
            enName: 'Greek',
            timestamp: getTimeStr(),
            baidu: 'el',          // 百度
            youDao: 'el',         // 有道
            huoShan: 'el',        // 火山
            xiaoNiu: 'el',        // 小牛
            google: 'el'          // 谷歌
        });
    }

// 插入捷克语
    const cs = await app.sdb.selectOne('language_list', {code: 'cs'});
    if (!cs) {
        app.sdb.insert('language_list', {
            code: 'cs',
            zhName: '捷克语',
            enName: 'Czech',
            timestamp: getTimeStr(),
            baidu: 'cs',          // 百度
            youDao: 'cs',         // 有道
            huoShan: 'cs',        // 火山
            xiaoNiu: 'cs',        // 小牛
            google: 'cs'          // 谷歌
        });
    }

// 插入瑞典语
    const sv = await app.sdb.selectOne('language_list', {code: 'sv'});
    if (!sv) {
        app.sdb.insert('language_list', {
            code: 'sv',
            zhName: '瑞典语',
            enName: 'Swedish',
            timestamp: getTimeStr(),
            baidu: 'swe',         // 百度使用瑞典语缩写
            youDao: 'sv',         // 有道
            huoShan: 'sv',        // 火山
            xiaoNiu: 'sv',        // 小牛
            google: 'sv'          // 谷歌
        });
    }

// 插入罗马尼亚语
    const ro = await app.sdb.selectOne('language_list', {code: 'ro'});
    if (!ro) {
        app.sdb.insert('language_list', {
            code: 'ro',
            zhName: '罗马尼亚语',
            enName: 'Romanian',
            timestamp: getTimeStr(),
            baidu: 'ro',          // 百度
            youDao: 'ro',         // 有道
            huoShan: 'ro',        // 火山
            xiaoNiu: 'ro',        // 小牛
            google: 'ro'          // 谷歌
        });
    }

// 插入匈牙利语
    const hu = await app.sdb.selectOne('language_list', {code: 'hu'});
    if (!hu) {
        app.sdb.insert('language_list', {
            code: 'hu',
            zhName: '匈牙利语',
            enName: 'Hungarian',
            timestamp: getTimeStr(),
            baidu: 'hu',          // 百度
            youDao: 'hu',         // 有道
            huoShan: 'hu',        // 火山
            xiaoNiu: 'hu',        // 小牛
            google: 'hu'          // 谷歌
        });
    }

// 插入丹麦语
    const da = await app.sdb.selectOne('language_list', {code: 'da'});
    if (!da) {
        app.sdb.insert('language_list', {
            code: 'da',
            zhName: '丹麦语',
            enName: 'Danish',
            timestamp: getTimeStr(),
            baidu: 'dan',         // 百度使用丹麦语缩写
            youDao: 'da',         // 有道
            huoShan: 'da',        // 火山
            xiaoNiu: 'da',        // 小牛
            google: 'da'          // 谷歌
        });
    }

// 插入芬兰语
    const fi = await app.sdb.selectOne('language_list', {code: 'fi'});
    if (!fi) {
        app.sdb.insert('language_list', {
            code: 'fi',
            zhName: '芬兰语',
            enName: 'Finnish',
            timestamp: getTimeStr(),
            baidu: 'fin',         // 百度使用芬兰语缩写
            youDao: 'fi',         // 有道
            huoShan: 'fi',        // 火山
            xiaoNiu: 'fi',        // 小牛
            google: 'fi'          // 谷歌
        });
    }

// 插入挪威语
    const no = await app.sdb.selectOne('language_list', {code: 'no'});
    if (!no) {
        app.sdb.insert('language_list', {
            code: 'no',
            zhName: '挪威语',
            enName: 'Norwegian',
            timestamp: getTimeStr(),
            baidu: 'nor',         // 百度使用挪威语缩写
            youDao: 'no',         // 有道
            huoShan: 'no',        // 火山
            xiaoNiu: 'no',        // 小牛
            google: 'no'          // 谷歌
        });
    }

// 插入希伯来语
    const iw = await app.sdb.selectOne('language_list', {code: 'iw'});
    if (!iw) {
        app.sdb.insert('language_list', {
            code: 'iw',
            zhName: '希伯来语',
            enName: 'Hebrew',
            timestamp: getTimeStr(),
            baidu: 'heb',         // 百度使用希伯来语缩写
            youDao: 'he',         // 有道
            huoShan: 'he',        // 火山
            xiaoNiu: 'he',        // 小牛
            google: 'iw'          // 谷歌使用旧代码iw（需注意兼容性）
        });
    }

    // 插入高棉语
    const km = await app.sdb.selectOne('language_list', {code: 'km'});
    if (!km) {
        app.sdb.insert('language_list', {
            code: 'km',
            zhName: '高棉语',
            enName: 'Khmer',
            timestamp: getTimeStr(),
            baidu: 'hkm',         // 百度使用希伯来语缩写
            youDao: 'km',         // 有道
            huoShan: 'km',        // 火山
            xiaoNiu: 'km',        // 小牛
            google: 'km'          // 谷歌
        });
    }

}

class Lifecycle {

    /**
     * core app have been loaded
     */
    async ready() {
        logger.info('[lifecycle] ready');
    }

    /**
     * electron app ready
     */
    async electronAppReady() {
        await initializeDatabase();
        await initializePlatform();
        await initializeTableData();
        app.viewsMap = new Map();
        app.baseUrl = baseUrl;
        // logger.info('[lifecycle] electron-app-ready');
        logger.info('init dataBase success！');
    }

    /**
     * main window have been loaded
     */
    async windowReady() {
        logger.info('[lifecycle] window-ready');
        // 延迟加载，无白屏
        const {windowsOption} = getConfig();
        if (windowsOption.show == false) {
            const win = getMainWindow();
            // 保存this引用
            const self = this;
            win.once('ready-to-show', () => {
                win.show();
                win.focus();

                // 在开发模式下绑定快捷键
                const config = getConfig();
                if (config.openDevTools) {
                    // 注册F5刷新快捷键
                    const f5Success = globalShortcut.register('F5', () => {
                        logger.info('[快捷键] F5 被按下，刷新页面');
                        win.webContents.reloadIgnoringCache();
                    });

                    // 注册Ctrl+Shift+I作为备用开发者工具快捷键
                    const ctrlShiftISuccess = globalShortcut.register('CmdOrCtrl+Shift+I', () => {
                        logger.info('[快捷键] Ctrl+Shift+I 被按下，打开/关闭开发者工具');
                        if (win.webContents.isDevToolsOpened()) {
                            win.webContents.closeDevTools();
                        } else {
                            win.webContents.openDevTools();
                        }
                    });
                    if (f5Success) {
                        logger.info('[lifecycle] F5=刷新 快捷键注册成功');
                    }
                    if (ctrlShiftISuccess) {
                        logger.info('[lifecycle] Ctrl+Shift+I=开发者工具 快捷键注册成功');
                    }
                }

                // 加载并应用系统设置
                self.loadAndApplySystemSettings();
            })
            // 主进程中全局拦截
            app.on('web-contents-created', (_, webContents) => {
                webContents.setWindowOpenHandler(({url}) => {
                    shell.openExternal(url).catch(() => {
                    });
                    return {action: 'deny'}; // 关键：阻止Electron创建窗口
                });

                // 为新创建的webContents应用缩放设置
                // 使用静态方法调用，避免this上下文问题
                try {
                    // 延迟应用设置，确保webContents完全初始化
                    setTimeout(() => {
                        if (!webContents.isDestroyed()) {
                            const {systemService} = require('../service/system');
                            const settings = systemService.loadSettings();
                            const pageZoomEnabled = settings.pageZoom === true;

                            systemService.setWebContentsZoom(webContents, pageZoomEnabled);
                        }
                    }, 100);
                } catch (error) {
                    logger.error('[lifecycle] 应用webContents缩放设置失败:', error);
                }
            });

        }
    }

    /**
     * 加载并应用系统设置
     */
    async loadAndApplySystemSettings() {
        try {
            const {systemService} = require('../service/system');
            const settings = systemService.loadSettings();

            // 应用页面缩放设置（确保总是有一个明确的值）
            const pageZoomSetting = settings.pageZoom === true; // 默认为false（禁用）
            systemService.setPageZoom(pageZoomSetting);

            logger.info('[lifecycle] 系统设置已加载并应用');
        } catch (error) {
            logger.error('[lifecycle] 加载系统设置失败:', error);
        }
    }

    /**
     * 为webContents应用缩放设置
     */
    applyZoomSettingToWebContents(webContents) {
        try {
            // 延迟应用设置，确保webContents完全初始化
            setTimeout(() => {
                if (!webContents.isDestroyed()) {
                    const {systemService} = require('../service/system');
                    const settings = systemService.loadSettings();
                    const pageZoomEnabled = settings.pageZoom === true;

                    systemService.setWebContentsZoom(webContents, pageZoomEnabled);
                }
            }, 100);
        } catch (error) {
            logger.error('[lifecycle] 应用webContents缩放设置失败:', error);
        }
    }

    /**
     * before app close
     */
    async beforeClose() {
        logger.info('[lifecycle] before-close');

        // 注销所有全局快捷键
        globalShortcut.unregisterAll();
        logger.info('[lifecycle] 全局快捷键已注销');
    }
}

Lifecycle.toString = () => '[class Lifecycle]';

module.exports = {
    Lifecycle
};
