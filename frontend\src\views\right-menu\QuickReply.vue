<template>
  <div class="quick-reply">
    <div class="header-container">
      <div class="header-title">
        <el-text tag="b" size="large">{{ t('quickReply.title') }}</el-text>
        <el-tooltip effect="dark" placement="top">
          <template #content>
            <div style="max-width: 200px">{{ t('quickReply.tooltipContent') }}</div>
          </template>
          <el-icon><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
    </div>
    <div class="header-search">
      <el-input :placeholder="t('quickReply.searchPlaceholder')" @input="handleSearch" v-model="searchKey" :suffix-icon="Search"> </el-input>
    </div>
    <div class="content-container">
      <el-empty v-if="groups.length <= 0" :description="t('quickReply.noData')" />
      <el-collapse class="collapse-item" v-model="activeNames">
        <el-collapse-item v-for="item in groups" :name="item.id">
          <template #title>
            <div class="collapse-item-title border">
              <div class="left-icon">
                <el-icon>
                  <ArrowDown v-if="isActive(item.id)" />
                  <ArrowRight v-else />
                </el-icon>
              </div>
              <div class="right-text">
                <el-text truncated>{{ item.name }}</el-text>
              </div>
            </div>
          </template>

          <template #default>
            <div class="collapse-item-content border-bottom">
              <el-empty v-if="item.contents.length <= 0" :image-size="60" :description="t('quickReply.noData')" />
              <div :style="{ backgroundColor: record.bgColor || '' }" v-for="record in item?.contents || []" @click="handleClick(record)" @mousedown="changeBgColor(record, 'var(--el-border-color)')" @mouseup="changeBgColor(record, '')">
                <div class="remark">
                  <div class="left">
                    <el-text tag="b" truncated>
                      {{ record.remark }}
                    </el-text>
                  </div>
                  <div class="right">
                    <el-button size="small" @click.stop="handleSend(record)" plain>{{ t('quickReply.send') }}</el-button>
                  </div>
                </div>
                <div class="content">
                  <el-tooltip :visible="record.visible || false" effect="dark" :content="record.content" placement="top-start">
                    <template #content>
                      <div style="max-width: 220px; max-height: 150px; overflow: auto">
                        {{ record.content }}
                      </div>
                    </template>
                    <el-text @mouseenter="record.visible = true" @mouseleave="record.visible = false" truncated>{{ record.content }}</el-text>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </template>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>
<script setup>
import { ArrowDown, ArrowRight, CaretBottom, CaretTop, QuestionFilled, Search } from '@element-plus/icons-vue';
import { computed, onMounted, ref } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import { useMenuStore } from '@/stores/menuStore';
import { searchCollection } from '@/utils/common';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const menuStore = useMenuStore();
onMounted(async () => {
  await initData();
});
const groups = ref([]);
const initData = async () => {
  const res = await ipc.invoke(ipcApiRoute.getGroups, {});
  if (res.status) {
    groups.value = res.data;
  }
};
const searchKey = ref('');
const handleSearch = async () => {
  if (searchKey.value === '') {
    await initData();
  }
  groups.value = searchCollection(groups.value, searchKey.value, true);
};
const activeNames = ref([]);
// 判断是否展开的通用方法
const isActive = computed(() => (name) => {
  return activeNames.value.includes(name);
});
const changeBgColor = (record, color) => {
  if (!record.bgColor) {
    record.bgColor = ''; // 初始化 bgColor 属性
  }
  record.bgColor = color;
};
const handleClick = (record) => {
  const args = {
    text: record.content,
    partitionId: menuStore.currentPartitionId,
    type: 'input'
  };
  ipc.invoke('send-msg', args);
};
const handleSend = (record) => {
  const args = {
    text: record.content,
    partitionId: menuStore.currentPartitionId,
    type: 'send'
  };
  ipc.invoke('send-msg', args);
};
</script>
<style scoped lang="scss">
.border-top {
  border-top: 1px solid var(--el-border-color);
}
.border-bottom {
  border-bottom: 1px solid var(--el-border-color);
}
.border {
  border-width: 1px 0; /* 上下边框宽度为 1px，左右边框宽度为 0 */
  border-style: solid;
  border-color: var(--el-border-color);
}
.quick-reply {
  width: 300px;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
  :deep(.el-input__wrapper) {
    border-radius: 0;
  }
  .header-container {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    user-select: none;
    justify-content: flex-start;
    :deep(.el-text) {
      --el-text-color: var(--el-text-color-primary);
    }
    .header-title {
      display: flex;
      align-items: center;
      flex: 1;
      gap: 5px;
      height: 30px;
    }
  }
  .header-search {
    width: 100%;
    margin-bottom: 20px;
  }
  .content-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    border: 1px solid var(--el-border-color);
    :deep(.el-collapse-item__header) {
      border-bottom: none;
    }
    :deep(.el-collapse-item__wrap) {
      border-bottom: none;
    }
    :deep(.el-collapse) {
      border-bottom: none;
      border-top: none;
    }
    :deep(.el-collapse-item__content) {
      padding-bottom: 0;
    }
    .collapse-item {
      width: 100%;
      height: 50px;
      .collapse-item-title {
        background-color: var(--el-bg-color);
        gap: 5px;
        display: flex;
        justify-content: start;
        align-items: center;
        width: 100%;
        .left-icon {
          display: flex;
          justify-content: end;
          align-items: center;
          width: 20px;
        }
        .right-text {
          display: flex;
          justify-content: start;
          align-items: center;
          max-width: 200px;
          flex: 1;
        }
      }
      .collapse-item-content {
        width: 100%;
        cursor: pointer;
        user-select: none;
        .remark {
          cursor: pointer;
          user-select: none;
          display: flex;
          align-items: center;
          height: 40px;
          width: 100%;
          .left {
            display: flex;
            align-items: center;
            flex: 1;
            height: 40px;
            max-width: 200px;
            padding-left: 10px;
          }
          .right {
            height: 40px;
            display: flex;
            align-items: center;
            min-width: 50px;
          }
        }
        .content {
          height: 30px;
          display: flex;
          padding-left: 10px;
          flex: 1;
        }
      }
    }
    .collapse-item :deep(.el-collapse-item__header .el-collapse-item__arrow) {
      display: none !important;
    }
  }
}
</style>
