import zhCN from 'element-plus/es/locale/lang/zh-cn';
import enUS from 'element-plus/es/locale/lang/en';
import { defineStore } from 'pinia';
import { useStorage } from '@vueuse/core';
import { isHttp } from '@/utils/validate';
import type { ApiLine } from '@/types/global';

export const useAppStore = defineStore('app', () => {
  const sidebarStatus = useStorage('sidebarStatus', '1');
  const sidebar = reactive({
    opened: sidebarStatus.value ? !!+sidebarStatus.value : true,
    withoutAnimation: false,
    hide: false
  });
  const device = ref<string>('desktop');
  const size = useStorage<'large' | 'default' | 'small'>('size', 'default');

  // 语言
  const language = useStorage('language', 'zh_CN');
  const languageObj: any = {
    en_US: enUS,
    zh_CN: zhCN
  };
  const locale = computed(() => {
    return languageObj[language.value];
  });
  // 默认的分组：普通分组和养号分组
  const defaultGroups = ref([
    {
      id: 1,
      groupName: '普通分组'
    },
    {
      id: 2,
      groupName: '养号分组'
    }
  ]);

  // 风险提醒弹窗
  const riskWarning = useStorage('riskWarning', {
    modifyTips: false,
    getMemberTips: false,
    massPostListRiskTips: false,
    massPostAddRiskTips: false,
    batchEntryTips: false,
    activeGroupListTips: false,
    activeGroupTips: false,
    groupChatTips: false,
    transferAccountTips: false,
    accountInfoTips: false
  });
  const toggleStatus = (type: string, val: boolean) => {
    if (type === 'modifyTips') {
      // 修改资料风险提醒
      riskWarning.value.modifyTips = val;
    } else if (type === 'getMemberTips') {
      // 群成员获取提醒
      riskWarning.value.getMemberTips = val;
    } else if (type === 'massPostListRiskTips') {
      // 群发列表页风险提醒
      riskWarning.value.massPostListRiskTips = val;
    } else if (type === 'massPostAddRiskTips') {
      // 群发新增页风险提醒
      riskWarning.value.massPostAddRiskTips = val;
    } else if (type === 'batchEntryTips') {
      // 进群风险提醒
      riskWarning.value.batchEntryTips = val;
    } else if (type === 'activeGroupListTips') {
      // 炒群列表页风险提醒
      riskWarning.value.activeGroupListTips = val;
    } else if (type === 'activeGroupTips') {
      // 炒群新增页风险提醒
      riskWarning.value.activeGroupTips = val;
    } else if (type === 'groupChatTips') {
      // 群聊群发风险提醒
      riskWarning.value.groupChatTips = val;
    } else if (type === 'transferAccountTips') {
      // 账号转移风险提醒
      riskWarning.value.transferAccountTips = val;
    } else if (type === 'accountInfoTips') {
      riskWarning.value.accountInfoTips = val;
    }
  };

  const toggleSideBar = (withoutAnimation: boolean) => {
    if (sidebar.hide) {
      return false;
    }

    sidebar.opened = !sidebar.opened;
    sidebar.withoutAnimation = withoutAnimation;
    if (sidebar.opened) {
      sidebarStatus.value = '1';
    } else {
      sidebarStatus.value = '0';
    }
  };

  const closeSideBar = ({ withoutAnimation }: any): void => {
    sidebarStatus.value = '0';
    sidebar.opened = false;
    sidebar.withoutAnimation = withoutAnimation;
  };
  const toggleDevice = (d: string): void => {
    device.value = d;
  };
  const setSize = (s: 'large' | 'default' | 'small'): void => {
    size.value = s;
  };
  const toggleSideBarHide = (status: boolean): void => {
    sidebar.hide = status;
  };

  const changeLanguage = (val: string): void => {
    language.value = val;
  };

  // 添加apiBaseUrl存储,默认使用环境变量中的baseURL
  let apiLink = import.meta.env.VITE_APP_BASE_API;
  if (!isHttp(apiLink)) {
    apiLink = window.location.origin + apiLink;
  }

  const apiBaseUrl = ref(apiLink);
  const apiLine = useStorage<ApiLine[]>('apiLine', []);
  const updateApiLine = (lines: ApiLine[]) => {
    apiLine.value = lines;
  };
  // 修改切换线路方法
  const changeApiLine = (baseUrl: string) => {
    apiBaseUrl.value = baseUrl;
  };

  return {
    device,
    sidebar,
    language,
    locale,
    defaultGroups,
    riskWarning,
    toggleStatus,
    size,
    changeLanguage,
    toggleSideBar,
    closeSideBar,
    toggleDevice,
    setSize,
    toggleSideBarHide,
    changeApiLine,
    apiBaseUrl,
    apiLine,
    updateApiLine
  };
});

export default useAppStore;
