<template>
  <div class="common-functions-container bg-white rounded-1 w-full h-full p6 border border-gray-100 shadow-sm">
    <h2 class="text-lg font-semibold mb-4">常用功能</h2>
    <div class="flex md:flex-row gap-4">
      <div class="ability" @click="router.push('/settings')">
        <div class="ability-icon flex items-center justify-center flex-shrink-0 rounded-1 bg-#10B981 mr3">
          <i class="iconfont icon-fanyi text-white"></i>
        </div>
        <div>
          <h3 class="font-medium font-size-3.5">翻译设置</h3>
          <p class="font-size-3 text-#8F959E">将作为新创建会话的默认翻译设置</p>
        </div>
      </div>
      <div class="ability" @click="goToProxySettings()">
        <div class="ability-icon flex items-center justify-center flex-shrink-0 rounded-1 bg-#0196FE mr3">
          <i class="iconfont icon-fuwuqi text-white"></i>
        </div>
        <div>
          <h3 class="font-medium font-size-3.5">代理设置 (全局)</h3>
          <p class="font-size-3 text-#8F959E">对于未进行单独设置的会话，代理...</p>
        </div>
      </div>
      <div class="ability">
        <div class="ability-icon flex items-center justify-center flex-shrink-0 rounded-1 bg-#FA8037 mr3">
          <i class="iconfont icon-jiaocheng text-white"></i>
        </div>
        <div>
          <h3 class="font-medium font-size-3.5">使用教程</h3>
          <p class="font-size-3 text-#8F959E">专注于帮助用户在跨语言环境下，进...</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();

// 跳转到代理设置页面
const goToProxySettings = () => {
  router.push({
    name: 'settings',
    query: { tab: 'agent' }
  });
};
</script>
<style lang="scss" scoped>
.ability {
  width: 33.333333%;
  height: 82px;
  display: flex;
  padding: 16px;
  align-items: center;
  border: 1px solid #ebebeb;
  text-align: left; /* 确保文字左对齐 */
  cursor: pointer;

  .ability-icon {
    width: 48px;
    height: 48px;

    i {
      font-size: 24px;
    }
  }
}
</style>
