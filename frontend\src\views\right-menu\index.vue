<template>
  <div ref="rightMenu" class="right-menu">
    <div v-show="isCollapsed" class="right-menu-container">
      <component :is="currentPage" />
    </div>
    <div class="right-menu-header">
      <!-- 折叠按钮 -->
      <div class="menu-item collapse-button" :class="{ active: isCollapsed }" @click="toggleCollapse">
        <el-icon :size="22" class="menu-icon">
          <component :is="isCollapsed ? FoldLeftIcon : FoldRightIcon" />
        </el-icon>
      </div>
      <!-- 操作按钮 -->
      <div v-for="(item, index) in menuItems" :key="index" class="menu-item" :class="{ active: activeMenu === item.action }" @click="selectMenuItem(item.action)">
        <el-icon :size="24" class="menu-icon">
          <component :is="item.icon" />
        </el-icon>
      </div>
      <div class="menu-item" @click="handleRefresh">
        <el-icon :size="24" class="menu-icon">
          <component :is="Refresh" />
        </el-icon>
      </div>
      <div class="menu-item" @click="handleClose">
        <el-icon :size="24" class="menu-icon">
          <component :is="CircleClose" />
        </el-icon>
      </div>
      <!-- <div class="menu-item" @click="handleDevTools">
       <el-icon :size="24" class="menu-icon">
         <component :is="ChromeFilled" />
       </el-icon>
     </div> -->
    </div>
  </div>
</template>

<script setup>
import { markRaw, onMounted, ref, watch } from 'vue';
import { ChromeFilled, CircleClose, Refresh, User } from '@element-plus/icons-vue';
import TranslateIcon from '@/components/icons/TranslateIcon.vue';
import QuickReplyIcon from '@/components/icons/QuickReplyIcon.vue';
import ServerIcon from '@/components/icons/ServerIcon.vue';
import FoldLeftIcon from '@/components/icons/FoldLeftIcon.vue';
import FoldRightIcon from '@/components/icons/FoldRightIcon.vue';
import TranslateConfig from '@/views/right-menu/TranslateConfig.vue';
import UserInfo from '@/views/right-menu/UserInfo.vue';
import QuickReply from '@/views/right-menu/QuickReply.vue';
import ProxyConfig from '@/views/right-menu/ProxyConfig.vue';
import { useMenuStore } from '@/stores/menuStore';
import { ipcApiRoute } from '@/api';
import { ipc } from '@/utils/ipcRenderer';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const menuStore = useMenuStore();

// 菜单页面配置
const pageItems = [
  { name: 'TranslateConfig', component: markRaw(TranslateConfig) },
  { name: 'UserInfo', component: markRaw(UserInfo) },
  { name: 'QuickReply', component: markRaw(QuickReply) },
  { name: 'ProxyConfig', component: markRaw(ProxyConfig) }
];

// 菜单图标配置
const menuItems = [
  { icon: markRaw(TranslateIcon), action: 'TranslateConfig' },
  { icon: markRaw(User), action: 'UserInfo' },
  { icon: markRaw(QuickReplyIcon), action: 'QuickReply' },
  { icon: markRaw(ServerIcon), action: 'ProxyConfig' }
];

// 响应式状态
const rightMenu = ref(null);
const currentPage = ref(markRaw(TranslateConfig));
const activeMenu = ref('TranslateConfig');
const isCollapsed = ref(menuStore.rightFoldStatus);
const devToolsStatus = ref(false);

// 监听当前激活的菜单，更新显示的组件
watch(
  () => activeMenu.value,
  (action) => {
    currentPage.value = pageItems.find((item) => item.name === action)?.component;
  },
  { immediate: true }
);

// 处理菜单宽度变化
const processWidth = () => {
  if (!rightMenu.value) return;

  const width = isCollapsed.value ? '350px' : '50px';
  rightMenu.value.style.width = width;
  menuStore.setRightFoldStatus(isCollapsed.value);
};

// 监听折叠状态变化
watch(() => isCollapsed.value, processWidth, { immediate: false });

// 菜单项选择处理
const selectMenuItem = (action) => {
  if (action === activeMenu.value) {
    // 当点击相同的菜单项时，切换折叠状态
    isCollapsed.value = !isCollapsed.value;
    return;
  }

  // 点击不同菜单项时，切换菜单并展开
  activeMenu.value = action;
  menuStore.setRightContent(activeMenu.value);
  isCollapsed.value = true;
};

// 折叠/展开处理
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 刷新当前会话
const handleRefresh = async () => {
  ElMessage({
    message: t('rightMenu.refreshTip'),
    type: 'warning',
    offset: 0
  });
  await ipc.invoke(ipcApiRoute.refreshSession, {
    partitionId: menuStore.currentMenu
  });
};

// 关闭当前会话
const handleClose = async () => {
  await ipc.invoke(ipcApiRoute.closeSession, {
    partitionId: menuStore.currentMenu
  });

  const menuRes = await ipc.invoke(ipcApiRoute.getSessionByPartitionId, {
    partitionId: menuStore.currentMenu
  });

  if (menuRes.status) {
    menuStore.updateChildrenMenu(menuRes.data.session);
    menuStore.setCurrentMenu(menuRes.data.session.platform);
  }
};

// 开发者工具控制
const handleDevTools = async () => {
  await ipc.invoke(ipcApiRoute.openSessionDevTools, {
    partitionId: menuStore.currentMenu,
    status: devToolsStatus.value
  });
  devToolsStatus.value = !devToolsStatus.value;
};
</script>

<style scoped lang="scss">
.right-menu {
  position: relative;
  height: calc(100vh - 30px);
  width: 50px;
  background-color: var(--el-bg-color);

  .right-menu-header {
    position: absolute;
    right: 0;
    top: 0;
    width: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: var(--el-bg-color);
    z-index: 2;

    .menu-item {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      cursor: pointer;

      &:not(.collapse-button).active {
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          width: 2px;
          height: 50px;
          background: var(--el-color-primary);
          border-radius: 0 2px 2px 0;
          animation: slideIn 0.3s ease;
        }
      }

      .menu-icon {
        color: var(--el-text-color-primary);
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: var(--el-color-primary-light-9);
        .menu-icon {
          color: var(--el-color-primary-light-3);
        }
      }

      &.active {
        .menu-icon {
          color: var(--el-color-primary);
          transform: scale(1.1);
          transition: all 0.3s ease;
        }
      }
    }
  }

  .right-menu-container {
    left: 0;
    top: 0;
    width: 300px;
    height: calc(100vh - 30px);
    overflow-y: auto;
    z-index: 1;
    background-color: var(--el-bg-color);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
