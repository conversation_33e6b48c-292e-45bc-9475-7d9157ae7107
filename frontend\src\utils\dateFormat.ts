export function formatChatTime(dateStr: string): string {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  const now = new Date();

  // 格式化时间的辅助函数
  const pad = (n: number) => (n < 10 ? `0${n}` : n);

  // 获取时间 HH:mm
  const getTime = (d: Date) => `${pad(d.getHours())}:${pad(d.getMinutes())}`;

  // 获取日期 MM-DD
  const getDate = (d: Date) => `${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;

  // 获取完整日期 YYYY-MM-DD
  const getFullDate = (d: Date) => `${d.getFullYear()}-${getDate(d)}`;

  // 判断是否是同一天
  const isSameDay = (d1: Date, d2: Date) => {
    return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
  };

  // 获取周几
  const weekMap = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const getWeekDay = (d: Date) => weekMap[d.getDay()];

  // 今天
  if (isSameDay(date, now)) {
    return getTime(date);
  }

  // 昨天
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (isSameDay(date, yesterday)) {
    return '昨天';
  }

  // 本周
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay());
  const endOfWeek = new Date(now);
  endOfWeek.setDate(startOfWeek.getDate() + 6);

  if (date >= startOfWeek && date <= endOfWeek) {
    return getWeekDay(date);
  }

  // 今年
  if (date.getFullYear() === now.getFullYear()) {
    return getDate(date);
  }

  // 往年
  return getFullDate(date);
}
