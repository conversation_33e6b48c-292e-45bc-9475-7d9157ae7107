<template>
  <div class="w-full h-full">
    <div class="data-title">当日数据</div>
    <el-descriptions :column="1" class="mt">
      <el-descriptions-item>
        <template #label>
          <div class="flex items-center">
            <div class="active-fans-dot">当日主动进粉：</div>
            <div>0</div>
          </div>
        </template>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="flex items-center">
            <div class="passive-fans-dot">当日被动进粉：</div>
            <div>0</div>
          </div>
        </template>
      </el-descriptions-item>
    </el-descriptions>
    <div class="h-1px bg-#DCDFE6 mt mb8"></div>
    <div class="data-title">历史数据</div>
    <el-descriptions :column="1" class="mt">
      <el-descriptions-item label="进粉总数：">0/0</el-descriptions-item>
      <el-descriptions-item label="主动/被动粉丝总数：">0/0</el-descriptions-item>
      <el-descriptions-item label="接收/回复消息：">0/0</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="scss" scoped>
.data-title {
  @apply text-#606266 font-size-3.5;
}

:deep(.el-descriptions__label) {
  color: #21262c;
}

.active-fans-dot:before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  margin-right: 5px;
}

.passive-fans-dot:before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #00a8ff;
  margin-right: 5px;
}
</style>
