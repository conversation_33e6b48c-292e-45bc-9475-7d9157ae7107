export default {
  common: {
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    search: 'Search',
    loading: 'Loading...',
    success: 'Success',
    failed: 'Failed',
    error: 'Error',
    warning: 'Warning',
    tip: 'Tip',
    yes: 'Yes',
    no: 'No',
    operation: 'Operation',
    copy: 'Copy',
    systemTip: 'System Tip',
    chinese: '简体中文',
    english: 'English',
    khmer: 'ភាសាខ្មែរ'
  },
  window: {
    minimize: 'Minimize',
    maximize: 'Maximize',
    restore: 'Restore',
    close: 'Close'
  },
  menu: {
    home: 'Home',
    subtitle: 'A2C Assistant',
    quickReply: 'Quick Reply',
    moreSetting: 'More Settings',
    translateConfig: 'Translate Config',
    customWeb: 'CustomWeb'
  },
  home: {
    // Header
    time: 'Current Time',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',
    copySuccess: 'Copied successfully!',
    copyFailed: 'Copy failed!',

    // Account Information
    accountInfo: 'Account Information',
    availableChars: 'Available Characters',
    expirationTime: 'Expiration Time',
    remainingDays: '{days} days remaining',
    deviceId: 'Device ID',

    // Core Features
    coreFeatures: 'Core Features',
    features: {
      multiAccount: {
        title: 'Multi-Account Login',
        desc: 'Support multiple accounts online simultaneously'
      },
      messageManagement: {
        title: 'Unified Message Management',
        desc: 'Centralized chat and notification handling'
      },
      translation: {
        title: 'Real-time Translation',
        desc: 'Bi-directional translation supporting 10+ languages'
      },
      localTranslation: {
        title: 'Local Translation',
        desc: 'Local API translation for data privacy'
      }
    },

    // Support and Help
    supportAndHelp: 'Support & Help',
    officialChannel: 'A2C Assistant Official Channel',
    channelDesc: 'Join our Telegram channel for latest announcements and tips',
    tags: {
      updates: 'Updates',
      support: 'Support',
      feedback: 'Feedback'
    },
    joinChannel: 'Join Channel'
  },
  login: {
    title: 'Welcome to use',
    loginKey: 'Please enter agent login key',
    loginButton: 'Login',
    forgotText: 'Forgot seat key? Please login to customer service management to view, ',
    gotoLink: 'Go Now',
    seatSelect: 'Please select seat type',
    keyHint: 'I previously registered 6, theoretically seat key quantity',
    rightTitle1: 'Use A2C，',
    rightTitle2: 'Everything you need to expand your business on WhatsApp',
    features: {
      feature1: 'Influencer statistics, fan notes/completion of accurate fan portraits',
      feature2: 'Tag management of users, precise push of marketing content, and construction of private domain traffic',
      feature3: 'Mass messaging, quick replies, and one click sending of various types of information',
      feature4: 'Supports over 200 languages worldwide, multiple translation routes, and provides real-time translation services'
    },
    partners: 'We will connect various marketing channels and multi-customer service methods within enterprises',
    agreement: {
      text: 'By logging in, you have read and agreed to',
      userAgreement: 'User Agreement',
      and: 'and',
      privacyPolicy: 'Privacy Policy'
    },
    autoLogin: 'Auto Login',
    validation: {
      required: 'Please enter agent login key',
      length: 'Key length should be between 5 and 20 characters',
      agreement: 'Please read and agree to the User Agreement and Privacy Policy'
    },
    lineSelector: {
      placeholder: 'Switch Line',
      defaultLine: 'Default Line',
      customLinePrompt: 'Enter custom line (supports http/https)',
      customLineTitle: 'Custom Line',
      inputPlaceholder: 'Please enter',
      inputErrorMessage: 'Please enter correct line address',
      lineExists: 'This line already exists',
      addSuccess: 'Line added successfully',
      addFailed: 'Failed to add line',
      refreshSuccess: 'Refresh successful',
      refreshFailed: 'Refresh failed'
    },
    logging: 'Logging in...',
    success: 'Login successful',
    errors: {
      emptyAuthCode: 'Please enter authorization key',
      invalidParams: 'Invalid parameters, please check input',
      accountNotExist: 'Account does not exist, please check authorization key',
      accountDisabled: 'Account has been disabled, please contact administrator',
      expired: 'Device authorization has expired, please get new authorization',
      deviceDisabled: 'Device has been disabled, please contact administrator',
      networkError: 'Network connection failed, please check network settings',
      unknown: 'Login failed, please try again later',
      authFailed: 'Authorization verification failed'
    }
  },
  session: {
    newChat: 'New Chat',
    nickname: 'Nickname',
    url: 'URL',
    proxyConfig: 'Proxy Config',
    proxyStatus: 'Proxy Status',
    proxyType: 'Proxy Type',
    proxyIp: 'Proxy IP',
    proxyPort: 'Proxy Port',
    username: 'Username',
    password: 'Password',
    sessionList: 'Session List',
    startAll: 'Start All',
    closeAll: 'Close All',
    batchDelete: 'Batch Delete',
    confirmDelete: 'Confirm delete selected sessions?',
    confirmDeleteSingle: 'Confirm delete this session?',
    remarks: 'Remarks',
    searchPlaceholder: 'Remarks, Username',
    createTime: 'Created At',
    sessionRecord: 'Session Record',
    show: 'Show',
    start: 'Start',
    close: 'Close',
    starting: 'Starting session, please wait!',
    closeSuccess: 'Session closed successfully',
    urlRequired: 'URL is required',
    proxyEnabled: 'Enable Authentication',
    proxyDisabled: 'Disable Authentication',
    delete: 'Delete',
    proxySettings: 'Proxy Settings',
    proxyConfigSaveSuccess: 'Proxy configuration saved successfully',
    enableProxy: 'Enable Proxy Server',
    proxyProtocol: 'Proxy Protocol',
    selectProxyProtocol: 'Select Proxy Protocol',
    hostAddress: 'Host Address',
    enterHostAddress: 'Please enter host address',
    portNumber: 'Port Number',
    enterPortNumber: 'Please enter port number',
    enableProxyAuth: 'Enable Proxy Authentication',
    enterUsername: 'Please enter username',
    enterPassword: 'Please enter password',
    restartRequired: 'Changes will take effect after restart'
  },
  translate: {
    google: 'Google Translate',
    baidu: 'Baidu Translate',
    youdao: 'YouDao Translate',
    huoshan: 'HuoShan Translate',
    xiaoniu: 'XiaoNiu Translate',
    apiKey: 'API Key',
    secretKey: 'Secret Key',
    appId: 'App ID',
    settings: 'Translation Settings',
    clearCacheTitle: 'Confirm clearing translation cache for current session?',
    mode: 'Translation Mode',
    localTranslate: 'Local',
    cloudTranslate: 'Cloud',
    tooltipContent: 'Local translation does not consume characters\nPlease configure translation API first',
    route: 'Translation Route',
    selectRoute: 'Please select translation route',
    realTimeReceive: 'Real-time Translation for Received Messages',
    realTimeSend: 'Real-time Translation for Sent Messages',
    sourceLanguage: 'Source Language',
    targetLanguage: 'Target Language',
    autoDetect: 'Auto Detect',
    friendIndependent: 'Independent Translation Switch for Friends',
    preview: 'Translation Preview'
  },
  quickReply: {
    title: 'Quick Reply',
    tooltipContent: 'Click to translate in input box\nDouble click to send original text',
    searchPlaceholder: 'Filter by title or content',
    noData: 'No Data',
    send: 'Send'
  },
  userInfo: {
    title: 'Contact Information',
    phoneNumber: 'Phone Number',
    nickname: 'Nickname',
    country: 'Country',
    gender: 'Gender',
    male: 'Male',
    female: 'Female',
    salesInfo: 'Sales Information',
    tradeActivity: 'Trade Activity',
    customerLevel: 'Customer Level',
    remarks: 'Remarks',
    enterRemarks: 'Please enter',
    followUpRecords: 'Follow-up Records',
    selectPlaceholder: 'Please select',
    // 交易活动状态
    activityStatus: {
      negotiating: 'Negotiating',
      scheduled: 'Scheduled',
      ordered: 'Ordered',
      paid: 'Paid',
      shipped: 'Shipped'
    },
    // 客户等级
    customerLevels: {
      normal: 'Normal',
      medium: 'Medium',
      important: 'Important',
      critical: 'Critical'
    }
  },
  rightMenu: {
    refreshTip: 'About to refresh current page',
    refresh: 'Refresh',
    close: 'Close',
    devTools: 'Developer Tools'
  },
  quickReplyConfig: {
    group: {
      title: 'Quick Reply Groups',
      addGroup: 'Add Group',
      editGroup: 'Edit Group',
      groupName: 'Group Name',
      enterGroupName: 'Please enter group name',
      deleteConfirm: 'Are you sure to delete this group?',
      filterPlaceholder: 'Filter by group name'
    },
    reply: {
      title: 'Quick Reply',
      addReply: 'Add Quick Reply',
      editReply: 'Edit Quick Reply',
      clearReply: 'Clear Replies',
      clearConfirm: 'Are you sure to clear all replies?',
      deleteConfirm: 'Are you sure to delete this quick reply?',
      selectGroup: 'Please select a group first',
      remark: 'Remark',
      enterRemark: 'Please enter remark',
      type: 'Type',
      content: 'Content',
      enterContent: 'Please enter content',
      text: 'Text',
      image: 'Image',
      video: 'Video',
      resource: 'Resource'
    },
    message: {
      addSuccess: 'Added successfully',
      editSuccess: 'Modified successfully',
      deleteSuccess: 'Deleted successfully',
      clearSuccess: 'Cleared successfully',
      operationFailed: 'Operation failed'
    }
  }
};
