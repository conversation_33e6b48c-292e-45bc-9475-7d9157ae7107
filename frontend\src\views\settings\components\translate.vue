<template>
  <div class="w-500px h-full">
    <div class="font-size-18px font-bold">翻译设置</div>
    <div class="txt">以下设置将作为新创建会话的默认翻译设置</div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">接收自动翻译设置</div>
      <div>
        <el-switch v-model="receiveAutoTranslate" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译路线</div>
      <div class="mt">
        <el-select v-model="translateRoute" placeholder="请选择翻译路线">
          <el-option label="自动检测" value="auto"></el-option>
          <el-option label="谷歌翻译" value="google"></el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译语言</div>
      <div class="mt">
        <el-select v-model="translateLanguage" placeholder="请选择翻译路线">
          <el-option label="自动检测" value="auto"></el-option>
          <el-option label="谷歌翻译" value="google"></el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">群组自动翻译</div>
      <div>
        <el-switch v-model="receiveAutoTranslate" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
      </div>
    </div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">发送自动翻译设置</div>
      <div>
        <el-switch v-model="receiveAutoTranslate" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译路线</div>
      <div class="mt">
        <el-select v-model="translateRoute" placeholder="请选择翻译路线">
          <el-option label="自动检测" value="auto"></el-option>
          <el-option label="谷歌翻译" value="google"></el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译语言</div>
      <div class="mt">
        <el-select v-model="translateLanguage" placeholder="请选择翻译路线">
          <el-option label="自动检测" value="auto"></el-option>
          <el-option label="谷歌翻译" value="google"></el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const receiveAutoTranslate = ref(false);
const translateRoute = ref('auto');
const translateLanguage = ref('auto');
</script>
<style lang="scss" scoped>
.txt {
  @apply text-#8F959E font-size-14px mt-10px;
}
</style>
