export interface loginData {
    clientId: string,
    grantType: string,
    seatLogin: boolean,
    seatLoginKey: string,
    systemVersion: number,
    autoLogin: boolean,
    deviceId: string,
    deviceInfo: any,
    agreement: boolean,

    [key: string]: any
}

// 登录响应数据类型
export interface LoginResponse {
    code: number;
    msg: string;
    data: {
        scope: string | null;
        openid: string | null;
        domain: string | null;
        access_token: string;
        refresh_token: string;
        expire_in: number;
        refresh_expire_in: number;
        client_id: string;
    };
}

// 简化的登录请求参数
export interface SimpleLoginParams {
    seatLoginKey: string;
    autoLogin: boolean;
}

// 用户信息类型
export interface UserInfo {
    userId: number;
    agentId: number | null;
    tenantId: string;
    deptId: number | null;
    userName: string;
    nickName: string;
    userType: string;
    email: string | null;
    phonenumber: string | null;
    sex: string | null;
    avatar: string | null;
    secondPwdEnabled: boolean | null;
    status: string | null;
    loginIp: string | null;
    loginDate: string | null;
    remark: string | null;
    createTime: string | null;
    deptName: string | null;
    roles: any | null;
    roleIds: any | null;
    postIds: any | null;
    roleId: any | null;
}

// 席位信息类型
export interface SeatInfo {
    id: number;
    talkUserId: string;
    groupId: number;
    platformType: number;
    seatName: string;
    seatAccount: string;
    seatPassword: string;
    clearTime: string | null;
    createTime: string;
    permissionJson: string;
    tenantId: string;
    shareCode: string | null;
    shortLinkUrl: string | null;
}

// 获取用户信息响应类型
export interface GetInfoResponse {
    code: number;
    msg: string;
    data: {
        user: UserInfo;
        roles: any | null;
        seatInfoVo: SeatInfo;
    };
}