import { defineStore } from 'pinia';
import { useStorage } from '@vueuse/core';
import { login as apiLogin, getInfo as apiGetInfo } from '@/api/common';
import { proxyConfigDetail } from '@/api/setting';
import type { LoginResponse, SimpleLoginParams, GetInfoResponse, UserInfo, SeatInfo } from '@/api/common/types';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';

export const useUserInfoStore = defineStore('userInfo', () => {
    // 存储在localStorage中的token信息
    const accessToken = useStorage('access_token', '');
    const refreshToken = useStorage('refresh_token', '');
    const tokenExpireTime = useStorage('token_expire_time', 0);
    const refreshExpireTime = useStorage('refresh_expire_time', 0);
    
    // 用户信息只存储在内存中，刷新时重新获取
    const userInfo = ref<UserInfo | null>(null);
    const roles = ref<any>(null);
    const seatInfo = ref<SeatInfo | null>(null);
    
    // 代理配置信息只存储在内存中，刷新时重新获取
    const proxyConfig = ref<{
        protocol: string;
        host: string;
        port: number;
        username: string;
        password: string;
        isActive: number;
    } | null>(null);
    
    // 用户登录状态
    const isLoggedIn = computed(() => {
        return !!accessToken.value && Date.now() < tokenExpireTime.value;
    });
    
    // 清除所有信息
    const clearAll = () => {
        accessToken.value = '';
        refreshToken.value = '';
        tokenExpireTime.value = 0;
        refreshExpireTime.value = 0;
        userInfo.value = null;
        roles.value = null;
        seatInfo.value = null;
        proxyConfig.value = null;
    };
    
    // 清除token信息
    const clearTokens = () => {
        accessToken.value = '';
        refreshToken.value = '';
        tokenExpireTime.value = 0;
        refreshExpireTime.value = 0;
    };
    
    // 清除用户信息（不清除token）
    const clearUserInfo = () => {
        userInfo.value = null;
        roles.value = null;
        seatInfo.value = null;
        proxyConfig.value = null;
    };
    
    // 设置token信息
    const setTokens = (data: LoginResponse['data']) => {
        accessToken.value = data.access_token;
        refreshToken.value = data.refresh_token;
        
        // 计算token过期时间（当前时间 + 过期秒数）
        const currentTime = Date.now();
        tokenExpireTime.value = currentTime + (data.expire_in * 1000);
        refreshExpireTime.value = currentTime + (data.refresh_expire_in * 1000);
    };
    
    // 设置用户信息
    const setUserInfo = (data: GetInfoResponse['data']) => {
        userInfo.value = data.user;
        roles.value = data.roles;
        seatInfo.value = data.seatInfoVo;
    };
    
    // 获取用户信息
    const getUserInfo = async (token?: string) => {
        try {
            // 使用传入的token或当前的accessToken
            const currentToken = token || accessToken.value;
            
            if (!currentToken) {
                console.log('没有有效的token，无法获取用户信息');
                return { success: false, message: '未登录' };
            }
            
            console.log('开始获取用户信息...');
            const response = await apiGetInfo() as GetInfoResponse;
            
            if (response.code === 200) {
                userInfo.value = response.data.user;
                roles.value = response.data.roles;
                seatInfo.value = response.data.seatInfoVo;
                
                console.log('用户信息获取成功:', userInfo.value);
                return { success: true, data: response.data };
            } else {
                console.error('获取用户信息失败:', response.msg);
                return { success: false, message: response.msg };
            }
        } catch (error) {
            console.error('获取用户信息过程发生错误:', error);
            return { success: false, message: '获取用户信息过程发生错误' };
        }
    };
    
    // 获取代理配置信息
    const getProxyConfigDetail = async () => {
        try {
            console.log('开始获取代理配置信息...');
            const response = await proxyConfigDetail() as any;
            
            if (response.code === 200) {
                proxyConfig.value = response.data;
                console.log('代理配置信息获取成功:', proxyConfig.value);
                return { success: true, data: response.data };
            } else {
                console.error('获取代理配置失败:', response.msg);
                proxyConfig.value = null;
                return { success: false, message: response.msg };
            }
        } catch (error) {
            console.error('获取代理配置过程发生错误:', error);
            proxyConfig.value = null;
            return { success: false, message: '获取代理配置过程发生错误' };
        }
    };
    
    // 刷新代理配置
    const refreshProxyConfig = async () => {
        console.log('手动刷新代理配置...');
        return await getProxyConfigDetail();
    };

    // 初始化用户信息（页面刷新时调用）
    const initUserInfo = async () => {
        // 如果已登录且没有用户信息，则重新获取
        if (isLoggedIn.value && !userInfo.value) {
            console.log('检测到已登录但无用户信息，重新获取用户信息');
            await getUserInfo();
        }
        
        // 无论是否登录都获取代理配置（因为代理配置可能是全局的）
        if (isLoggedIn.value) {
            console.log('获取代理配置信息');
            await getProxyConfigDetail();
        }
    };
    
    // 登录逻辑
    const login = async (params: SimpleLoginParams) => {
        try {
            // 获取系统信息
            const systemInfo = await ipc.invoke(ipcApiRoute.getSystemInfo, {});
            
            // 构建完整的登录数据
            const deviceInfo = {
                name: systemInfo.name || '',
                platform: systemInfo.platform || '',
                version: systemInfo.version || '',
                osName: systemInfo.osName || '',
                arch: systemInfo.arch || ''
            };
            
            const loginData = {
                clientId: import.meta.env.VITE_APP_CLIENT_ID as string,
                grantType: 'seatLoginKey',
                seatLogin: true,
                seatLoginKey: params.seatLoginKey,
                systemVersion: 14000,
                autoLogin: params.autoLogin,
                deviceId: systemInfo.machineCode || '',
                deviceInfo: JSON.stringify(deviceInfo),
                agreement: true
            };
            
            console.log('登录数据:', JSON.stringify(loginData, null, 2));
            
            // 调用登录API
            const response = await apiLogin(loginData) as LoginResponse;
            
            if (response.code === 200) {
                // 登录成功，保存token信息
                setTokens(response.data);
                
                console.log('登录成功，token已保存');
                
                // 登录成功后立即获取用户信息，传入最新的token确保使用正确的认证
                const userInfoResult = await getUserInfo(response.data.access_token);
                
                if (userInfoResult.success) {
                    console.log('用户信息已获取并保存');
                    return { success: true, data: response.data, userInfo: userInfoResult.data };
                } else {
                    console.warn('登录成功但获取用户信息失败:', userInfoResult.message);
                    return { success: true, data: response.data, warning: '获取用户信息失败' };
                }
            } else {
                console.error('登录失败:', response.msg);
                return { success: false, message: response.msg };
            }
        } catch (error) {
            console.error('登录过程发生错误:', error);
            return { success: false, message: '登录过程发生错误' };
        }
    };
    
    // 退出登录
    const logout = () => {
        clearAll();
        console.log('已退出登录，所有信息已清除');
    };
    
    return {
        accessToken: readonly(accessToken),
        refreshToken: readonly(refreshToken),
        userInfo: readonly(userInfo),
        roles: readonly(roles),
        seatInfo: readonly(seatInfo),
        proxyConfig: readonly(proxyConfig),
        isLoggedIn,
        login,
        logout,
        clearTokens,
        setTokens,
        getUserInfo,
        getProxyConfigDetail,
        refreshProxyConfig,
        setUserInfo,
        clearAll,
        clearUserInfo,
        initUserInfo
    };
}); 