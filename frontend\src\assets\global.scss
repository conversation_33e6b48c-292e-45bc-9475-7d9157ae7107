* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: 1rem;
  line-height: 1.5;
  color: #21262c;
  background-color: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#app {
  height: 100%;
}
li{
  list-style: none;
}
.theme-color {
  color: #10b981;
}
a {
  text-decoration: none;
  color: #10b981;
}

i {
  font-style: normal;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;

  &:hover {
  }
}

// 自定义样式
.border-r-1{
  border-right: 1px solid #d1d7db;
}
.flex-center{
  @apply flex items-center justify-center;
}
.flex-between{
  @apply flex items-center justify-between;
}

.a2c-circle-btn.el-button.is-circle {
  border: none;
  width: 40px;
  height: 40px;
  &:hover {
    background-color: rgba(217, 219, 222, 0.3);
    color: #4c5a64;
  }
  &+.a2c-circle-btn {
    margin-left: 5px;
  }
}
.a2c-dropdown {
  .el-popper__arrow {
    display: none;
    opacity: 0;
  }
  &.el-popper{
    border-radius: 10px;
    min-width: 100px;
    max-width: 200px;
    padding-top: 10px;
    .el-dropdown-menu{
      padding: 10px;
      border-radius: 10px;
    }
    .el-dropdown-menu__item{
      padding: 10px;
      border-radius: 10px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}

.icon-avatar,
.icon-group-avatar {
  display: inline-block;
  vertical-align: middle;
  width: 38px;
  height: 38px;
  background-image: url(@/assets/icons/png/avatar.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}