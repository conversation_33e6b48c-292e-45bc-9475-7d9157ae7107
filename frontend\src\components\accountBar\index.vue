<template>
  <div class="w-246px h-full bg-white px-10px pt-20px flex flex-col border-r-1" :class="{ 'is-fold': isFold }">
    <header class="flex items-center justify-between">
      <h1 class="text-4 font-bold">W<i>hat</i>s<i>App</i></h1>
      <el-icon class="cursor-pointer" @click="handleFold">
        <Fold v-if="!isFold" />
        <Expand v-else />
      </el-icon>
    </header>
    <div class="flex items-center justify-between mt-4 mb-10px account-btn">
      <el-button type="primary">
        <i v-if="!isFold">新建会话</i>
        <el-icon class="ml-1"> <Plus /> </el-icon>
      </el-button>
      <el-button type="primary" plain>
        <i v-if="!isFold">一键登录</i>
        <svg-icon icon-class="up" class="ml-1" />
      </el-button>
    </div>
    <!-- 账号类型 tab -->
    <TypeTab v-if="!isFold" v-model="currentAccountType" @change="handleAccountType" />
    <!-- 账号列表查询 -->
    <SearchFrom v-if="!isFold" />
    <!-- 账号列表 -->
    <el-scrollbar ref="scrollbarRef" class="flex-1 account-list mt-10px ml--1">
      <AccountList />
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { Fold, Plus, Search, Top, Expand } from '@element-plus/icons-vue';
import { useAppStore } from '@/stores/modules/app';
import TypeTab from './components/TypeTab.vue';
import SearchFrom from './components/SearchFrom.vue';
import AccountList from './components/AccountList.vue';
import { useAccountStore } from '@/stores/modules/account';

const appStore = useAppStore();
const accountStore = useAccountStore();
const { isFold } = storeToRefs(accountStore);

const currentAccountType = ref('all');

const handleFold = () => {
  isFold.value = !isFold.value;
};

const handleAccountType = (value: string) => {
  currentAccountType.value = value;
};
</script>

<style lang="scss" scoped>
.is-fold {
  width: 85px;
  header {
    h1 > i {
      display: none;
    }
  }
  // 账号按钮
  .account-btn {
    .el-button {
      width: 28px;
      height: 32px;
      padding: 0;
      .ml-1 {
        margin-left: 0;
      }
    }
  }
}
</style>
