<template>
  <div class="my-index-page">
    <div ref="leftMenu" class="left-menu">
      <div class="left-menu-header">
        <div @click="toggleMenuHeader(homeMenuData.id)" :class="activeMenu === 'Home' ? 'active-header-menu' : 'default-header-menu'">
          <div class="header-menu-line" />
          <div class="header-menu-img">
            <el-badge :offset="[-3, 6]" :value="homeMenuData.children.length" :max="99" :show-zero="false" type="info">
              <el-avatar>
                <el-icon size="40" :color="homeMenuData.color">
                  <component :is="homeMenuData.icon" />
                </el-icon>
              </el-avatar>
            </el-badge>
          </div>
          <div v-if="isCollapse" class="header-menu-title">
            <el-text>{{ homeMenuData.title }}</el-text>
          </div>
        </div>
      </div>
      <el-scrollbar class="menu-item-group">
        <div v-for="menu in menuStore.menus" :key="menu.id">
          <div
            @click="toggleMenu(menu)"
            :class="{
              'active-menu-item-child': (isActive(menu.id) || hasActiveChild(menu)) && (hasChildren(menu) || hasActiveChild(menu)),
              'active-menu-item': (isActive(menu.id) || hasActiveChild(menu)) && !hasChildren(menu) && !hasActiveChild(menu),
              'default-menu-item': !isActive(menu.id) && !hasActiveChild(menu)
            }"
          >
            <div class="menu-item-line" />
            <div class="menu-item-img">
              <el-badge :offset="[-6, 2]" :value="filteredChildren(menu.children).length" :max="99" :show-zero="false" type="info">
                <el-avatar>
                  <el-icon size="50" :color="menu.color">
                    <component :is="menu.icon" />
                  </el-icon>
                </el-avatar>
              </el-badge>
            </div>
            <div v-if="isCollapse" class="menu-item-text">
              <el-text>{{ menu.title.startsWith('menu.') ? t(menu.title) : menu.title }}</el-text>
            </div>
            <div v-if="isCollapse && filteredChildren(menu.children).length > 0" class="menu-item-icon">
              <el-icon v-if="!(menu.openChildren || hasActiveChild(menu))" size="15">
                <ArrowDown />
              </el-icon>
              <el-icon v-if="menu.openChildren || hasActiveChild(menu)" size="15">
                <ArrowUp />
              </el-icon>
            </div>
          </div>
          <!--          子菜单区域-->
          <div v-if="menu.openChildren === true && menu.children && menu.children.length > 0" class="submenu-container" :key="`${menu.id}-${filteredChildren(menu.children).length}`">
            <div
              v-for="child in filteredChildren(menu.children)"
              :key="child.partitionId"
              @click="toggleSubMenu(child)"
              :class="{
                'active-child-menu-item': menuStore.currentMenu === child.partitionId,
                'default-child-menu-item': menuStore.currentMenu !== child.partitionId
              }"
            >
              <div class="child-menu-item-line" />
              <div class="child-menu-item-img" :class="{ 'margin-top-10': !isEmpty(child.avatarUrl) }">
                <el-badge :offset="[-38, 15]" is-dot :type="child.onlineStatus === 'true' ? 'success' : 'info'">
                  <el-badge v-if="!isCollapse && child.onlineStatus === 'true'" :offset="[0, 0]" :value="child.msgCount" :max="99" :show-zero="false" type="success" class="msg-badge">
                    <!-- 如果有头像，展示头像 -->
                    <el-avatar v-if="child.avatarUrl" :src="child.avatarUrl" style="height: 30px; width: 30px" />
                    <!-- 如果没有头像，展示默认头像 -->
                    <template v-else>
                      <el-avatar style="height: 30px; width: 30px">
                        <el-icon v-if="!isCustomWeb(menu)">
                          <User />
                        </el-icon>
                        <el-icon color="#30ace1" v-else>
                          <component :is="google" />
                        </el-icon>
                      </el-avatar>
                    </template>
                  </el-badge>
                  <el-badge v-else :offset="[0, 0]" :value="0" :max="99" :show-zero="false" type="danger">
                    <!-- 如果有头像，展示头像 -->
                    <el-avatar v-if="child.avatarUrl" :src="child.avatarUrl" style="height: 30px; width: 30px" />
                    <!-- 如果没有头像，展示默认头像 -->
                    <template v-else>
                      <el-avatar style="height: 30px; width: 30px">
                        <el-icon v-if="!isCustomWeb(menu)">
                          <User />
                        </el-icon>
                        <el-icon size="35" color="#30ace1" v-else>
                          <component :is="google" />
                        </el-icon>
                      </el-avatar>
                    </template>
                  </el-badge>
                </el-badge>
              </div>
              <!--              折叠隐藏部分-->
              <div v-if="isCollapse" class="child-menu-item-text">
                <div class="text-center-container">
                  <div v-if="child.nickName && child.nickName" class="child-menu-item-text-nickname">
                    <el-text>{{ child.nickName }}</el-text>
                  </div>
                  <div v-if="child.userName && child.userName" class="child-menu-item-text-username">
                    <el-text>{{ child.userName }}</el-text>
                  </div>
                  <div v-if="child.remarks && child.remarks !== ''" class="child-menu-item-text-remarks">
                    <el-text>{{ child.remarks }}</el-text>
                  </div>
                </div>
              </div>
              <div v-if="isCollapse" class="child-menu-item-icon margin-top-10">
                <el-badge v-if="child.onlineStatus && child.onlineStatus === 'true'" :offset="[-15, 0]" type="success" :show-zero="false" :max="99" :value="child?.msgCount"></el-badge>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>

      <!--      底部区域-->
      <div class="fold-area">
        <div class="fold-header"></div>

        <div class="fold-menu">
          <div @click="toggleBottomMenu('QuickReply')" :class="activeMenu === 'QuickReply' ? 'active-fold-menu-item' : 'default-fold-menu-item'">
            <div class="menu-item-line" />
            <div class="menu-item-img">
              <el-avatar>
                <el-icon size="25">
                  <component :is="quickReply" />
                </el-icon>
              </el-avatar>
            </div>
            <div v-if="isCollapse" class="menu-item-text">
              <el-text>{{ t('menu.quickReply') }}</el-text>
            </div>
          </div>
          <div @click="toggleBottomMenu('TranslateConfig')" :class="activeMenu === 'TranslateConfig' ? 'active-fold-menu-item' : 'default-fold-menu-item'">
            <div class="menu-item-line" />
            <div class="menu-item-img">
              <el-avatar>
                <el-icon size="25">
                  <component :is="translateSetting" />
                </el-icon>
              </el-avatar>
            </div>
            <div v-if="isCollapse" class="menu-item-text">
              <el-text>{{ t('menu.translateConfig') }}</el-text>
            </div>
          </div>
          <!--          <div @click="toggleBottomMenu('MoreSetting')" :class="activeMenu === 'MoreSetting'?'active-fold-menu-item':'default-fold-menu-item'">-->
          <!--            <div class="menu-item-line"/>-->
          <!--            <div class="menu-item-img">-->
          <!--              <el-avatar>-->
          <!--                <el-icon size="25"><Setting /></el-icon>-->
          <!--              </el-avatar>-->
          <!--            </div>-->
          <!--            <div v-if="isCollapse" class="menu-item-text">-->
          <!--              <el-text>更多设置</el-text>-->
          <!--            </div>-->
          <!--          </div>-->
        </div>

        <div @click="toggleCollapse" class="fold-button">
          <el-button :icon="isCollapse ? ArrowLeft : ArrowRight" circle class="collapse-btn" :class="{ 'is-collapsed': isCollapse }" />
        </div>
      </div>
    </div>

    <div class="main-content">
      <component :is="currentComponent" />
    </div>

    <div v-show="menuStore.isChildMenu" class="right-menu">
      <RightMenu />
    </div>
  </div>
</template>

<script setup>
import RightMenu from './right-menu/index.vue';
import Home from '@/views/home/<USER>';
import SessionList from '@/views/session-list/index.vue';
import QuickReply from '@/views/quick-reply/index.vue';
import TranslateConfig from '@/views/translate-config/index.vue';
import Unknown from '@/views/components/un-known/index.vue';

import quickReply from '@/components/icons/QuickReplyIcon.vue';
import translateSetting from '@/components/icons/TranslateSettingIcon.vue';
import logo from '@/components/icons/LogoIcon.vue';
import google from '@/components/icons/GoogleIcon.vue';
import { ArrowLeft, ArrowRight, User, ArrowDown, ArrowUp, Fold, Expand } from '@element-plus/icons-vue';

import { ref, markRaw, watch, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { ElButton, ElMessageBox } from 'element-plus';

import { useMenuStore } from '@/stores/menuStore';
import { ipcApiRoute } from '@/api';
import { ipc } from '@/utils/ipcRenderer';
import router from '@/router';
import { useI18n } from 'vue-i18n';

const menuStore = useMenuStore();
const { t } = useI18n();

const currentComponent = ref(markRaw(Home));
const isCollapse = ref(true);
const activeMenu = ref('Home');
const leftMenu = ref(null);

const homeMenuData = computed(() => ({
  id: 'Home',
  title: t('menu.home'),
  color: '#30ace1',
  subtitle: t('menu.subtitle'),
  icon: markRaw(logo),
  openChildren: false,
  children: []
}));

const isCustomWeb = (menu) => {
  return menu.id === 'CustomWeb';
};

const isEmpty = (value) => {
  if (value == null) return true;

  if (typeof value === 'string') {
    return value.trim().length === 0;
  }

  return false;
};

let timer = null;
const clearTimer = () => {
  if (timer) {
    clearInterval(timer);
  }
};

const filteredChildren = computed(() => (menu) => {
  const nMenus = menu.filter((child) => child.windowStatus === 'true') || [];
  return nMenus;
});

const isActive = (menuId) => menuStore.currentMenu === menuId;

const hasActiveChild = (menu) => {
  const nMenus = menu.children.filter((child) => child.windowStatus === 'true') || [];
  return nMenus.some((child) => child.partitionId === menuStore.currentMenu);
};

const hasChildren = (menu) => {
  const nMenus = menu.children.filter((child) => child.windowStatus === 'true') || [];
  return nMenus.length > 0;
};

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value;
  if (leftMenu.value) {
    leftMenu.value.style.width = isCollapse.value ? '200px' : '65px';
  }
};

const toggleMenu = async (menu) => {
  if (menuStore.currentMenu === menu.id && menu?.children?.length === 0) {
    return;
  }

  activeMenu.value = menu.id;
  menu.openChildren = !menu.openChildren;
  menuStore.setCurrentMenu(menu.id);
  await ipc.invoke(ipcApiRoute.hiddenSession, {});
};

const toggleMenuHeader = async (menuName) => {
  if (menuStore.currentMenu === menuName) {
    return;
  }

  activeMenu.value = menuName;
  menuStore.setCurrentMenu(menuName);
  await ipc.invoke(ipcApiRoute.hiddenSession, {});
  menuStore.setIsChildMenu(menuName);
};

const toggleBottomMenu = async (menuName) => {
  if (menuStore.currentMenu === menuName) {
    return;
  }

  await ipc.invoke(ipcApiRoute.hiddenSession, {});
  activeMenu.value = menuName;
  menuStore.setCurrentMenu(menuName);
  menuStore.setIsChildMenu(menuName);
};

const toggleSubMenu = async (child) => {
  if (menuStore.currentMenu === child.partitionId) {
    return;
  }

  await ipc.invoke(ipcApiRoute.showSession, {
    platform: child.platform,
    partitionId: child.partitionId
  });

  activeMenu.value = child.partitionId;
  menuStore.setCurrentMenu(child.partitionId);
  menuStore.setCurrentPlatform(child.platform);
  menuStore.setCurrentPartitionId(child.partitionId);
};

const authExpire = (msg) => {
  ElMessageBox.confirm(`${msg}`, t('common.systemTip'), {
    showClose: false,
    showCancelButton: false,
    confirmButtonText: t('common.confirm'),
    type: 'error',
    closeOnClickModal: false,
    closeOnPressEscape: false
  })
    .then(async () => {
      await router.push('/login');
    })
    .catch(() => {});
};
const networkErrorCount = ref(0); // 新增网络错误计数
const checkLogin = async () => {
  const authKey = menuStore.userInfo?.authKey;
  if (authKey) {
    const res = await ipc.invoke(ipcApiRoute.login, { authKey });

    // 处理网络错误的特殊情况
    if (!res.status && res.message === 'login.errors.networkError') {
      networkErrorCount.value++;
      // 只有当网络错误超过3次才执行登出逻辑
      if (networkErrorCount.value >= 3) {
        clearTimer();
        await ipc.invoke(ipcApiRoute.hiddenSession, {});
        authExpire(t(res.message));
        networkErrorCount.value = 0; // 重置网络错误计数
      }
      return;
    }
    // 处理其他错误情况
    if (!res.status) {
      clearTimer();
      await ipc.invoke(ipcApiRoute.hiddenSession, {});
      authExpire(t(res.message));
    } else {
      // 授权成功时重置所有计数
      networkErrorCount.value = 0;
      menuStore.setUserInfo(res.data);
    }
    console.log('check user auth:', res.data);
  } else {
    clearTimer();
    await ipc.invoke(ipcApiRoute.hiddenSession, {});
    authExpire(t('login.errors.authFailed'));
  }
};

const menuItems = [
  { id: 'Home', component: markRaw(Home) },
  { id: 'Telegram', component: markRaw(SessionList) },
  { id: 'WhatsApp', component: markRaw(SessionList) },
  { id: 'TikTok', component: markRaw(SessionList) },
  { id: 'QuickReply', component: markRaw(QuickReply) },
  { id: 'TranslateConfig', component: markRaw(TranslateConfig) },
  { id: 'CustomWeb', component: markRaw(SessionList) },
  { id: 'Unknown', component: markRaw(Unknown) }
];

watch(
  () => menuStore.currentMenu,
  (newValue) => {
    const selectedMenuItem = menuItems.find((item) => item.id === newValue);
    currentComponent.value = selectedMenuItem ? selectedMenuItem.component : markRaw(Unknown);

    menuStore.setIsChildMenu(newValue);
  }
);

onMounted(() => {
  const handleOnlineNotify = (event, args) => {
    const { data } = args;
    menuStore.updateChildrenMenu(data);
  };

  ipc.removeAllListeners('online-notify');
  ipc.on('online-notify', handleOnlineNotify);

  initMenuSessions();

  clearTimer();
  timer = setInterval(checkLogin, 60000 * 10);
});

onUnmounted(() => {
  clearTimer();
});

const initMenuSessions = async () => {
  for (let menu of menuStore.menus) {
    const res = await ipc.invoke(ipcApiRoute.getSessions, { platform: menu.id });
    if (res.status) {
      const arr = res.data.sessions;
      menuStore.setMenuChildren(menu.id, arr);
    }
  }
};
</script>

<style scoped lang="scss">
/* 常用边距工具类 */
.margin-top-10 {
  margin-top: 10px;
}

.margin-right-15 {
  padding-right: 15px;
}

/* 主页面布局 */
.my-index-page {
  height: calc(100vh - 30px);
  display: flex;
  justify-content: flex-start;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

/* 左侧菜单区域 */
.left-menu {
  width: 200px;
  height: 100%;
  cursor: pointer;
  user-select: none;
  position: relative;
  background-color: var(--el-bg-color);

  /* 顶部菜单头部 */
  .left-menu-header {
    height: 60px;

    /* 菜单项基础样式 */
    .default-header-menu,
    .active-header-menu {
      display: flex;
      height: 60px;

      .header-menu-line {
        width: 3px;
      }

      .header-menu-img {
        margin-left: 10px;
        margin-top: 10px;
        margin-right: 15px;
        width: 30px;
        height: 30px;

        :deep(.el-avatar) {
          --el-avatar-bg-color: var(--el-bg-color);
        }
      }

      .header-menu-title,
      .header-menu-subtitle {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex: 1;
        height: 60px;
        text-align: left;
        color: var(--el-text-color-primary);
      }
    }

    /* 活跃菜单样式 */
    .active-header-menu {
      background-color: var(--el-color-primary-light-9);

      .header-menu-line {
        background-color: var(--el-color-primary);
      }
    }

    /* 默认菜单样式 */
    .default-header-menu {
      .header-menu-line {
        background-color: var(--el-bg-color);
      }
    }
  }

  /* 菜单项组 */
  .menu-item-group {
    height: calc(100% - 235px);

    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }

    :deep(.el-scrollbar__bar.is-horizontal) {
      display: none;
    }

    :deep(.el-scrollbar__bar.is-vertical) {
      display: none;
    }

    :deep(.msg-badge .el-badge__content) {
      font-size: 9px;
      height: 16px;
      line-height: 16px;
      padding: 0 4px;
      border-radius: 8px;
    }

    /* 菜单项通用样式 */
    .default-menu-item,
    .active-menu-item,
    .active-menu-item-child {
      display: flex;
      height: 60px;
      transition: all 0.3s ease;

      .menu-item-line {
        width: 3px;
        transition: background-color 0.3s ease;
      }

      .menu-item-img {
        margin-left: 10px;
        margin-top: 10px;
        margin-right: 15px;
        width: 30px;
        height: 30px;
        transition: all 0.3s ease;

        :deep(.el-avatar) {
          --el-avatar-bg-color: var(--el-bg-color);
        }
      }

      .menu-item-text,
      .active-menu-item-text {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex: 1;
        height: 60px;
        text-align: left;
        transition: color 0.3s ease;
      }

      .menu-item-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        transition: all 0.3s ease;
      }
    }

    /* 默认菜单项样式 */
    .default-menu-item {
      color: var(--el-text-color-primary);

      .menu-item-line {
        background-color: var(--el-bg-color);
      }
    }

    /* 有子菜单的激活菜单项 */
    .active-menu-item-child {
      background-color: var(--el-bg-color);

      :deep(.el-text) {
        --el-text-color: var(--el-color-primary);
      }

      .menu-item-line {
        background-color: var(--el-bg-color);
      }

      .menu-item-text {
        color: var(--el-color-primary);
      }
    }

    /* 激活菜单项样式 */
    .active-menu-item {
      background-color: var(--el-color-primary-light-9);

      .menu-item-line {
        background-color: var(--el-color-primary);
      }

      .menu-item-icon {
        justify-content: flex-start;
      }
    }

    /* 子菜单项样式 */
    .default-child-menu-item,
    .active-child-menu-item {
      display: flex;
      height: 60px;
      align-items: center;
      padding-right: 0;
      box-sizing: border-box;
      position: relative;
      transition: all 0.3s ease;

      .child-menu-item-line {
        width: 3px;
        transition: all 0.3s ease;
      }

      .child-menu-item-img {
        display: flex;
        flex: 0 0 30px;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        margin-left: 20px;
        transition: all 0.3s ease;
      }

      .child-menu-item-text {
        height: 60px;
        display: flex;
        flex: 1;
        flex-direction: column;
        min-width: 0;
        padding-left: 10px;
        justify-content: center;
        transition: all 0.3s ease;

        .text-center-container {
          display: flex;
          flex-direction: column;
          justify-content: center;
          gap: 2px;
          transition: all 0.3s ease;
        }

        &-nickname,
        &-username,
        &-remarks {
          min-height: 0;
          padding: 0 4px;
          display: flex;
          align-items: center;
          line-height: 1.2;
          transition: all 0.3s ease;

          .el-text {
            font-size: 10px;
            color: var(--el-text-color-primary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            transition: color 0.3s ease;
          }
        }
      }

      .child-menu-item-icon {
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 0 0 30px;
        margin-right: 5px;
        transition: all 0.3s ease;
      }
    }

    /* 默认子菜单项 */
    .default-child-menu-item {
      color: var(--el-text-color-primary);

      .child-menu-item-line {
        background-color: var(--el-bg-color);
      }
    }

    /* 激活子菜单项 */
    .active-child-menu-item {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-text-color-primary);

      .child-menu-item-line {
        display: flex;
        width: 3px;
        height: 60px;
        background-color: var(--el-color-primary);
      }

      .child-menu-item-text {
        .text-center-container {
          .child-menu-item-text-nickname,
          .child-menu-item-text-username,
          .child-menu-item-text-remarks {
            .el-text {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }
  }

  /* 底部折叠区域 */
  .fold-area {
    height: 175px;
    width: 100%;
    position: absolute;
    bottom: 0;

    .fold-header {
      height: 5px;
      width: 100%;
      background-color: var(--el-bg-color-page);
    }

    /* 底部菜单 */
    .fold-menu {
      height: 120px;

      /* 通用底部菜单项样式 */
      .default-fold-menu-item,
      .active-fold-menu-item {
        display: flex;
        height: 60px;

        .menu-item-line {
          width: 3px;
        }

        .menu-item-img {
          margin-left: 10px;
          margin-top: 10px;
          margin-right: 15px;
          width: 30px;
          height: 30px;

          :deep(.el-avatar) {
            --el-avatar-bg-color: var(--el-color-primary);
          }
        }

        .menu-item-text {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex: 1;
          height: 60px;
          text-align: left;
        }

        .menu-item-icon {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          flex: 1;
        }
      }

      /* 默认底部菜单项 */
      .default-fold-menu-item {
        .menu-item-line {
          background-color: var(--el-bg-color);
        }
      }

      /* 激活底部菜单项 */
      .active-fold-menu-item {
        background-color: var(--el-color-primary-light-9);

        .menu-item-line {
          background-color: var(--el-color-primary);
        }
      }
    }

    /* 折叠按钮区域 */
    .fold-button {
      display: flex;
      height: 50px;
      align-items: center;
      justify-content: center;
      border-top: 1px solid var(--el-border-color);
      box-sizing: border-box;

      .collapse-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        border: 1px solid var(--el-border-color);
        background-color: var(--el-bg-color);

        &:hover {
          background-color: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary);
          color: var(--el-color-primary);
        }

        &.is-collapsed {
          /* 移除旋转效果 */
        }

        :deep(.el-icon) {
          font-size: 16px;
        }
      }
    }
  }
}

/* 内容区域 */
.main-content {
  flex: 1;
  min-width: 0;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  padding: 10px;
}

/* 右侧菜单 */
.right-menu {
  position: relative;
  height: calc(100vh - 30px);
  background-color: var(--el-bg-color);
}

/* 子菜单展开/收起动画 */
.submenu-container {
  /* 移除过渡效果 */
  /* 移除 height transition */
  /* 移除 will-change 属性 */
}

/* 删除所有这些动画相关的类 */
.submenu-enter-from,
.submenu-leave-to {
  height: 0 !important;
}

.submenu-enter-active,
.submenu-leave-active {
  transition: height 0.3s ease-in-out;
  overflow: hidden;
}

.submenu-enter-to,
.submenu-leave-from {
  height: auto;
}

/* 子菜单项样式 */
.default-child-menu-item,
.active-child-menu-item {
  // ... existing styles ...
}
</style>
