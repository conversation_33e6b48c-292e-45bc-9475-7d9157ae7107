/* Element Plus 主题色配置 */
:root {
  /* 主色调 */
  --el-color-primary: #10b981 !important;

  /* 主色调的浅色变体 */
  --el-color-primary-light-3: #34d399 !important;
  --el-color-primary-light-5: #6ee7b7 !important;
  --el-color-primary-light-7: #a7f3d0 !important;
  --el-color-primary-light-8: #d1fae5 !important;
  --el-color-primary-light-9: #ecfdf5 !important;

  /* 主色调的深色变体 */
  --el-color-primary-dark-2: #059669 !important;

  /* 成功色 */
  --el-color-success: #10b981 !important;
  --el-color-success-light-3: #34d399 !important;
  --el-color-success-light-5: #6ee7b7 !important;
  --el-color-success-light-7: #a7f3d0 !important;
  --el-color-success-light-8: #d1fae5 !important;
  --el-color-success-light-9: #ecfdf5 !important;
  --el-color-success-dark-2: #059669 !important;
}

/* 强制覆盖Element Plus的默认样式 */
.el-button--primary:not(.is-plain) {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
  color: #ffffff !important;
}

.el-button--primary:not(.is-plain):hover {
  background-color: #34d399 !important;
  border-color: #34d399 !important;
  color: #ffffff !important;
}

.el-button--primary:not(.is-plain):active {
  background-color: #059669 !important;
  border-color: #059669 !important;
  color: #ffffff !important;
}

.el-button--primary:not(.is-plain):focus {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
  color: #ffffff !important;
}

/* Plain 按钮样式 */
.el-button--primary.is-plain {
  background-color: transparent !important;
  border-color: #10b981 !important;
  color: #10b981 !important;
}

.el-button--primary.is-plain:hover {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
  color: #ffffff !important;
}

.el-button--primary.is-plain:active {
  background-color: #059669 !important;
  border-color: #059669 !important;
  color: #ffffff !important;
}

.el-button--primary.is-plain:focus {
  background-color: transparent !important;
  border-color: #10b981 !important;
  color: #10b981 !important;
}

/* 下拉选择器 */
.el-select .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #10b981 inset !important;
}

.el-select .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px #10b981 inset !important;
}

.el-select-dropdown__item.selected {
  color: #10b981 !important;
  font-weight: bold !important;
}

.el-select-dropdown__item:hover {
  background-color: #ecfdf5 !important;
  color: #10b981 !important;
}

/* 菜单项 */
.el-menu-item.is-active {
  color: #10b981 !important;
  background-color: #ecfdf5 !important;
}

.el-menu-item:hover {
  color: #10b981 !important;
  background-color: #f0fdf4 !important;
}

/* 表格 */
.el-table th.el-table__cell {
  background-color: #f9fafb !important;
}

.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #ecfdf5 !important;
}

/* 分页 */
.el-pagination.is-background .el-pager li:not(.is-disabled).is-active {
  background-color: #10b981 !important;
  color: #ffffff !important;
}

.el-pagination.is-background .el-pager li:not(.is-disabled):hover {
  color: #10b981 !important;
}

/* 标签 */
.el-tag--success {
  background-color: #ecfdf5 !important;
  border-color: #d1fae5 !important;
  color: #10b981 !important;
}

/* 进度条 */
.el-progress-bar__outer {
  background-color: #e5e7eb !important;
}

.el-progress-bar__inner {
  background-color: #10b981 !important;
}

/* 开关 */
.el-switch.is-checked .el-switch__core {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
}

/* 滑块 */
.el-slider__runway {
  background-color: #e5e7eb !important;
}

.el-slider__bar {
  background-color: #10b981 !important;
}

.el-slider__button {
  border-color: #10b981 !important;
}

/* 时间选择器 */
.el-date-picker__header-label.active {
  color: #10b981 !important;
}

.el-date-picker__header-label:hover {
  color: #34d399 !important;
}

.el-picker-panel__icon-btn:hover {
  color: #10b981 !important;
}

.el-date-table td.available:hover {
  color: #10b981 !important;
}

.el-date-table td.current:not(.disabled) {
  color: #ffffff !important;
  background-color: #10b981 !important;
}

/* 输入框焦点状态 */
.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #10b981 inset !important;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px #10b981 inset !important;
}

/* 链接 */
.el-link {
  color: #10b981 !important;
}

.el-link:hover {
  color: #34d399 !important;
}

.el-link:active {
  color: #059669 !important;
}

/* 消息框 */
.el-message--success {
  background-color: #ecfdf5 !important;
  border-color: #d1fae5 !important;
  color: #10b981 !important;
}

/* 通知 */
.el-notification--success {
  background-color: #ecfdf5 !important;
  border-color: #d1fae5 !important;
  color: #10b981 !important;
}

/* 警告框 */
.el-alert--success {
  background-color: #ecfdf5 !important;
  border-color: #d1fae5 !important;
  color: #10b981 !important;
}

/* 对话框 */
.el-dialog__headerbtn:hover .el-dialog__close {
  color: #10b981 !important;
}

/* 抽屉 */
.el-drawer__header {
  color: #10b981 !important;
}

/* 标签页 */
.el-tabs__item.is-active {
  color: #10b981 !important;
}

.el-tabs__item:hover {
  color: #10b981 !important;
}

.el-tabs__active-bar {
  background-color: #10b981 !important;
}

/* 步骤条 */
.el-step__head.is-process {
  color: #10b981 !important;
}

.el-step__title.is-process {
  color: #10b981 !important;
}

.el-step__head.is-finish {
  color: #10b981 !important;
}

.el-step__title.is-finish {
  color: #10b981 !important;
}

/* 树形控件 */
.el-tree-node__content:hover {
  background-color: #ecfdf5 !important;
}

.el-tree-node.is-current > .el-tree-node__content {
  background-color: #d1fae5 !important;
  color: #10b981 !important;
}

/* 级联选择器 */
.el-cascader-node.is-active {
  color: #10b981 !important;
  background-color: #ecfdf5 !important;
}

.el-cascader-node:hover {
  background-color: #f0fdf4 !important;
}

/* 自动完成 */
.el-autocomplete-suggestion li.highlighted {
  background-color: #ecfdf5 !important;
  color: #10b981 !important;
}

/* 颜色选择器 */
.el-color-picker__trigger {
  border-color: #10b981 !important;
}

/* 上传组件 */
.el-upload-dragger:hover {
  border-color: #10b981 !important;
}

/* 评分组件 */
.el-rate__icon.hover {
  color: #10b981 !important;
}

.el-rate__icon.is-active {
  color: #10b981 !important;
}
