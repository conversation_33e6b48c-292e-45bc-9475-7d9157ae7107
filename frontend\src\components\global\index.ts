import { Component } from 'vue';

interface ModuleMap {
  [key: string]: {
    default: Component;
  };
}

interface GlobalComponents {
  [key: string]: Component;
}

const modules = import.meta.glob('./*.vue', { eager: true }) as ModuleMap;
const map: GlobalComponents = {};

Object.keys(modules).forEach((file) => {
  const modulesName = file.replace('./', '').replace('.vue', '');
  map[modulesName] = modules[file].default;
});

const globalComponents: GlobalComponents = {
  ...map
};

export default globalComponents;
