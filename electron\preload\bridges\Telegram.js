/*
 * 如果启用了上下文隔离，渲染进程无法使用electron的api，
 * 可通过contextBridge 导出api给渲染进程使用
 */
const { contextBridge, ipcRenderer } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
    ipcRenderer: ipcRenderer,
    loginNotify: (args) => {
        return ipcRenderer.invoke('online-notify', args);
    },
    infoUpdate: (args) => {
        return ipcRenderer.invoke('info-update', args);
    },
    getTranslateConfig: (args) => {
        return ipcRenderer.invoke('translate-config', args);
    },
    //{provider,text,code}
    translateText: (args) => {
        return ipcRenderer.invoke('text-translate', args);
    }
})
