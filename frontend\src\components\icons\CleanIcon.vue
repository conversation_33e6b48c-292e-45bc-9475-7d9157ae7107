<template>
  <svg t="1740650326413" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8806" :width="size" :height="size">
    <path
      d="M954.368 920.832l-60.586667-349.269333h20.48c16.440889 0 29.696-13.255111 29.696-29.724445v-219.420444c0-16.440889-13.255111-29.724444-29.696-29.724445h-281.144889V84.707556c0-16.469333-13.255111-29.724444-29.724444-29.724445h-182.869333c-16.440889 0-29.696 13.255111-29.696 29.724445v208.014222H109.681778c-16.469333 0-29.724444 13.255111-29.724445 29.696v219.420444c0 16.469333 13.255111 29.724444 29.724445 29.724445h20.48l-60.586667 349.269333a29.667556 29.667556 0 0 0 29.240889 34.730667h826.311111a29.582222 29.582222 0 0 0 29.269333-34.730667zM159.971556 372.707556h310.840888V134.997333h82.289778v237.710223h310.869334v118.869333H159.971556v-118.869333z m534.840888 502.869333v-178.289778a9.159111 9.159111 0 0 0-9.130666-9.159111h-54.869334a9.159111 9.159111 0 0 0-9.130666 9.159111v178.289778h-219.420445v-178.289778a9.159111 9.159111 0 0 0-9.159111-9.159111h-54.840889a9.159111 9.159111 0 0 0-9.159111 9.159111v178.289778H158.606222l51.541334-297.159111h603.534222l51.541333 297.159111h-170.382222z"
      :fill="color"
      p-id="8807"
    ></path>
  </svg>
</template>

<script setup>
defineProps({
  size: [Number, String],
  color: {
    type: String,
    default: 'currentColor' // 默认继承父级颜色
  }
});
</script>
