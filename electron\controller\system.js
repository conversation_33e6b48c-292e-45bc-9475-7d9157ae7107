'use strict';

const { logger } = require('ee-core/log');
const {systemService} = require("../service/system");
const {app} = require("electron");
const {windowService} = require("../service/window");

/**
 * SystemController 获取系统基本信息
 * @class
 */
class SystemController {

    async getBaseInfo(args,event) {
        return await systemService.getBaseInfo(args,event);
    }

    async login(args,event) {
        return await systemService.login(args,event);
    }
    async logOut(args, event) {
        for (const [key, view] of app.viewsMap) {
            await windowService._destroyView(key)
        }
        return {status:true,message:'操作成功'}
    }

    async getSystemSettings(args, event) {
        return await systemService.getSystemSettings(args, event);
    }

    async updateSystemSettings(args, event) {
        return await systemService.updateSystemSettings(args, event);
    }

    async selectCacheDirectory(args, event) {
        return await systemService.selectCacheDirectory(args, event);
    }

    async getCacheDirectory(args, event) {
        return await systemService.getCacheDirectory(args, event);
    }

    async getCacheInfo(args, event) {
        return await systemService.getCacheInfo(args, event);
    }

    async clearCache(args, event) {
        return await systemService.clearCache(args, event);
    }

    async clearDatabaseCache(args, event) {
        return await systemService.clearDatabaseCache(args, event);
    }

    async clearAllCache(args, event) {
        return await systemService.clearAllCache(args, event);
    }
  /**
   * HTTP请求代理
   */
  async httpRequest(args, event) {
    return await systemService.httpRequest(args, event);
  }

  /**
   * 本地代理连接测试
   */
  async testProxyConnection(args, event) {
    return await systemService.testProxyConnection(args, event);
  }
}
SystemController.toString = () => '[class SystemController]';

module.exports = SystemController;
