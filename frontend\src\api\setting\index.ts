import ipcRequest from '../../utils/ipcRequest';
import {IPConfig} from './types.ts'
import { ipc } from '@/utils/ipcRenderer'
import { ipcApiRoute } from '@/api'

// 客户端检查代理状态
export function checkProxyStatus(data: IPConfig) {
    return ipcRequest({
        url: '/talk/ipPool/client/checkProxyStatus',
        method: 'post',
        data
    });
}

// 本地代理连接测试
export function testProxyConnection(data: IPConfig) {
    return ipc.invoke(ipcApiRoute.testProxyConnection, {
        host: data.host,
        port: data.port,
        ipMold: data.ipMold,
        userId: data.userId,
        password: data.password,
        enableAuth: data.userId && data.password
    });
}

// 获取客户端席位全局代理配置详细信息
export function proxyConfigDetail() {
    return ipcRequest({
        url: '/talk/client/ProxyConfig/global/detail',
        method: 'get',
    });
}
