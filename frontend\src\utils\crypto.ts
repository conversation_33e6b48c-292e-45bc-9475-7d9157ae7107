import CryptoJS from 'crypto-js';

/**
 * 生成随机的加密密钥
 * @returns 32字符的随机十六进制字符串
 */
export const generateEncryptionKey = (): string => {
  return CryptoJS.lib.WordArray.random(128 / 8).toString();
};

/**
 * 使用AES加密文本
 * @param plaintext 要加密的明文
 * @param key 加密密钥
 * @returns 加密后的密文
 */
export const encryptText = (plaintext: string, key: string): string => {
  const encrypted = CryptoJS.AES.encrypt(plaintext, key).toString();
  return encrypted;
};

/**
 * 使用AES解密文本
 * @param ciphertext 要解密的密文
 * @param key 解密密钥
 * @returns 解密后的明文
 */
export const decryptText = (ciphertext: string, key: string): string => {
  const decrypted = CryptoJS.AES.decrypt(ciphertext, key);
  return decrypted.toString(CryptoJS.enc.Utf8);
};

/**
 * 验证解密是否成功
 * @param decryptedText 解密后的文本
 * @returns 是否解密成功
 */
export const isDecryptionValid = (decryptedText: string): boolean => {
  return decryptedText.length > 0;
};

/**
 * 加密用户密钥的完整流程
 * @param userKey 用户输入的密钥
 * @returns 包含加密密钥和加密后的用户密钥的对象
 */
export const encryptUserKey = (userKey: string) => {
  const encryptionKey = generateEncryptionKey();
  const encryptedUserKey = encryptText(userKey, encryptionKey);

  return {
    encryptionKey,
    encryptedUserKey
  };
};

/**
 * 解密用户密钥的完整流程
 * @param encryptedUserKey 加密后的用户密钥
 * @param encryptionKey 加密密钥
 * @returns 解密后的用户密钥，失败返回null
 */
export const decryptUserKey = (encryptedUserKey: string, encryptionKey: string): string | null => {
  try {
    const decryptedKey = decryptText(encryptedUserKey, encryptionKey);

    if (isDecryptionValid(decryptedKey)) {
      return decryptedKey;
    }

    return null;
  } catch (error) {
    console.error('Decryption failed:', error);
    return null;
  }
};

// 密钥管理相关接口类型定义
export interface KeyStatus {
  hasLocalKey: boolean;
  hasSystemKey: boolean;
  localKeyExpired: boolean | null;
  keyCreatedAt?: string;
  keyExpiresAt?: string;
}

export interface KeyValidationResult {
  status: boolean;
  message: string;
  needRegenerate?: boolean;
  data?: {
    finalEncryptionKey: string;
    keyCreatedAt: string;
    keyExpiresAt: string;
  };
}

export interface KeyGenerationResult {
  status: boolean;
  message: string;
  data?: {
    generatedKey: string;
    finalEncryptionKey: string;
    systemStoreSuccess: boolean;
  };
}
