<template>
  <div class="right-menu flex">
    <div :class="{ expanded: currentValue !== '' }" class="right-menu-panel">
      <transition mode="out-in" name="content-fade">
        <div v-if="currentValue !== ''" class="h-full right-menu-content">
          <div class="w-full h-11 flex justify-between items-center content-header px4">
            <span class="whitespace-nowrap">{{ menu.find((item) => item.value === currentValue).label }}</span>
            <i class="iconfont icon-shouqi transform rotate-180 cursor-pointer" @click="currentValue = ''" />
          </div>
          <div class="w-full panel-content px4 pt6" v-if="true">
            <component :is="currentViewList[currentValue]"></component>
          </div>
          <div v-else class="w-full h-full flex justify-center items-center">
            <el-empty description="该账号不支持使用该设置" />
          </div>
        </div>
      </transition>
    </div>
    <div class="w16 h-full right-menu-content">
      <div v-for="(item, index) in menu" :key="index" class="font-size-3 text-center text-#86909C">
        <div :class="{ active: currentValue === item.value }" class="mt4 flex justify-center items-center cursor-pointer flex-col py3" @click="handleClick(item.value)">
          <i :class="item.icon" class="iconfont !font-size-18px"></i>
          <span class="mt2">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import translateSetting from '@/components/rightMenu/components/translateSetting.vue';
import quickReply from '@/components/rightMenu/components/quickReply.vue';
import replySetting from '@/components/rightMenu/components/replySetting.vue';
import replyUser from '@/components/rightMenu/components/replyUser.vue';
import fansStatistics from '@/components/rightMenu/components/fansStatistics.vue';
import proxySettings from '@/components/rightMenu/components/proxySettings.vue';

const currentViewList = {
  translateSetting,
  quickReply,
  replySetting,
  replyUser,
  fansStatistics,
  proxySettings
};
const menu = ref([
  {
    label: '翻译设置',
    value: 'translateSetting',
    icon: 'icon-fanyi'
  },
  {
    label: '快捷回复',
    value: 'quickReply',
    icon: 'icon-quanjushezhi'
  },
  {
    label: '回复设置',
    value: 'replySetting',
    icon: 'icon-huifu'
  },
  {
    label: '客户资料',
    value: 'replyUser',
    icon: 'icon-geren'
  },
  {
    label: '进粉统计',
    value: 'fansStatistics',
    icon: 'icon-tongji'
  },
  {
    label: '代理设置',
    value: 'proxySettings',
    icon: 'icon-fuwuqi'
  }
]);
const currentValue = ref('');
const handleClick = (value: string) => {
  if (currentValue.value === value) {
    currentValue.value = '';
  } else {
    currentValue.value = value;
  }
  console.log(value);
};
</script>
<style lang="scss" scoped>
.right-menu {
  height: 100%;
  background: #fff;
}

.content-header {
  border-bottom: 1px solid #e5e7eb;
}

.active {
  @apply bg-#F0F2F5 text-#10B981;
  border-radius: 6px;
}

.right-menu-content {
  border-left: 1px solid #e5e7eb;
}

/* 右侧面板容器动画 */
.right-menu-panel {
  width: 0;
  overflow: hidden;
  transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.right-menu-panel.expanded {
  width: 270px;
}

/* 面板内容区域保持固定位置 - 关键修改 */
.right-menu-panel .right-menu-content {
  width: 270px !important; /* 强制固定宽度，不受父容器影响 */
  white-space: nowrap;
  flex-shrink: 0; /* 防止flex布局压缩 */
}

/* 内容header也保持固定宽度 */
.content-header {
  width: 270px;
  flex-shrink: 0;
}

/* 面板内容也保持固定宽度 */
.panel-content {
  width: 270px;
  height: calc(100% - 2.75rem);
  flex-shrink: 0;
}

/* 标题文字处理 */
.content-header span {
  white-space: nowrap;
}

/* 内容淡入淡出动画 */
.content-fade-enter-active {
  transition: opacity 0.3s ease-out;
}

.content-fade-leave-active {
  transition: opacity 0.2s ease-in;
}

.content-fade-enter-from,
.content-fade-leave-to {
  opacity: 0;
}

.content-fade-enter-to,
.content-fade-leave-from {
  opacity: 1;
}
</style>
