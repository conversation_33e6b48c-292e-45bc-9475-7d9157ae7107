<template>
  <div class="flex-1 flex items-center justify-center">
    <el-text type="info" class="text-xs select-none"> {{ currentTime }} | 工作区：{{ workspaceName }} </el-text>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';

const currentTime = ref('');
const workspaceName = ref('主工作区');
let timer: any = null;

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });
};

onMounted(() => {
  updateTime();
  timer = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style scoped>
.no-drag {
  -webkit-app-region: no-drag;
}
</style>
