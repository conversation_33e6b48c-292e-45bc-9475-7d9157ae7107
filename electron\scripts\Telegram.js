const ipc = window.electronAPI;
let userInfo = {};
let trcConfig = {}
//========================用户基本信息获取开始============
const getCurrentUserId = (item) => {
    if (!item){
        // 获取当前页面 URL 中 # 号后的部分
        const hashValue = window.location.hash;
        // 去除开头的 '#'
        const id = hashValue.slice(1);
        return id;
    }
    //获取当前会话的userId
    const peerElement = item ? item.querySelector('[data-peer-id]') : null;
    // 获取 data-peer-id 的值
    const peerId = peerElement ? peerElement.getAttribute('data-peer-id') : null;
    return peerId;
}
const onlineStatusCheck = ()=> {
    setInterval(async () => {
        const element = localStorage.dc1_auth_key;
        const main = document.getElementById('Main');
        const {avatarUrl,nickName,phoneNumber,userName} = await getUserInfo();
        if (element || main) {
            const msgCount = await getNewMsgCount()
            const args = {
                platform: 'Telegram',
                onlineStatus: true,
                avatarUrl: avatarUrl,
                nickName:nickName,
                phoneNumber:phoneNumber,
                userName:userName,
                msgCount:msgCount
            }
            ipc.loginNotify(args);
            userInfo = args;
        } else {
            const args = {
                platform: 'Telegram',
                onlineStatus: false,
                avatarUrl: avatarUrl,
                nickName:nickName,
                phoneNumber:phoneNumber,
                userName:userName,
                msgCount:0
            }
            ipc.loginNotify(args);
            userInfo = args;
        }
    }, 3000); // 每隔5000毫秒（3秒）调用一次
}
const getUserInfo = () => {
    return new Promise((resolve, reject) => {
        // 统一获取用户ID的逻辑（避免重复）
        const getUserId = () => {
            try {
                const userAuthString = localStorage.getItem("user_auth");
                if (!userAuthString) return null;
                return JSON.parse(userAuthString).id;
            } catch (error) {
                console.error("解析用户ID失败:", error);
                return null;
            }
        };

        const request = indexedDB.open("tt-data");

        request.onsuccess = function(event) {
            const db = event.target.result;
            const transaction = db.transaction("store", "readonly");
            const objectStore = transaction.objectStore("store");
            const getRequest = objectStore.get("tt-global-state");

            // 初始化返回对象
            const userInfo = {
                phoneNumber: '',
                nickName: '',
                userName: '',
                avatarUrl: ''
            };

            getRequest.onsuccess = function(event) {
                const data = event.target.result;
                if (!data?.users) {
                    resolve(userInfo); // 返回空数据
                    return;
                }

                const userId = getUserId();
                if (!userId) {
                    resolve(userInfo);
                    return;
                }

                // 处理基础用户信息
                const user = data.users.byId?.[userId];
                if (user) {
                    userInfo.phoneNumber = user.phoneNumber || '';
                    const firstName = user.firstName || '';
                    const lastName = user.lastName || '';
                    userInfo.nickName = `${firstName} ${lastName}`.trim();

                    if (user.usernames?.length > 0) {
                        userInfo.userName = `@${user.usernames[0].username}`;
                    }
                }

                // 处理头像信息
                const profilePhoto = data.users.fullInfoById?.[userId]?.profilePhoto;
                if (profilePhoto?.thumbnail?.dataUri) {
                    userInfo.avatarUrl = profilePhoto.thumbnail.dataUri;
                }

                resolve(userInfo); // 最终返回数据
            };

            getRequest.onerror = () => reject(new Error('读取数据库失败'));
        };

        request.onerror = () => reject(new Error('打开数据库失败'));
    });
};
const getNewMsgCount = () => {
    try {
        let newCount = 0;

        // 查询所有未读且未静音的聊天标识
        const chatNodes = document.querySelectorAll("div.ChatBadge.unread:not(.muted)");

        chatNodes.forEach(node => {
            const span = node.querySelector("span");
            if (span) {
                const rawValue = span.textContent.trim();
                const value = parseInt(rawValue, 10);

                // 累加有效数值
                if (!isNaN(value) && value >= 0) {
                    newCount += value;
                } else {
                    console.debug(`忽略无效值：${rawValue}`);  // 调试日志代替警告
                }
            }
        });

        return newCount;  // 直接返回统计结果

    } catch (e) {
        console.error('消息统计异常：', e.message);
        return 0;  // 异常时返回默认值
    }
};
onlineStatusCheck();
//========================用户基本信息获取结束============

//中文检测
const containsChinese = (text)=> {
    const regex = /[\u4e00-\u9fa5]/;  // 匹配中文字符的正则表达式
    return regex.test(text);  // 如果包含中文字符返回 true，否则返回 false
}
const sendMsg = ()=> {
    let sendButton = document.querySelectorAll('button.Button.send.main-button.default.secondary.round.click-allowed')[0]
    if (sendButton) {
        sendButton.click();
    }else {
        console.log('发送按钮不存在！')
    }
}
const sendTranslate = async (text,to)=>{
    if (text && text.trim()) {
        const route = trcConfig.translateRoute;
        styledTextarea.setContent('翻译中...')
        styledTextarea.setIsProcessing(true);
        const mode = trcConfig.mode;
        const res = await ipc.translateText({route: route, text: text, to: to,isFilter:'true',mode:mode});
        if (res.status) {
            //调用ipc获取翻译结果
            styledTextarea.setContent(res.data);
            styledTextarea.setTranslateStatus(true)
            styledTextarea.setIsProcessing(false);
        }else {
            styledTextarea.setContent(res.message);
            styledTextarea.setTranslateStatus(false)
            styledTextarea.setIsProcessing(false);
        }
    }
}
const createStyledTextarea = () => {
    const container = document.createElement('div');
    Object.assign(container.style, {
        display: 'flex',
        flexDirection: 'column',
    });
    container.id = 'custom-translate-textarea';
    // 标题元素
    const label = document.createElement('span');
    Object.assign(label.style, {
        fontSize: '12px',
        color: 'green',
        marginLeft: '20px',
        marginBottom: '10px',
        marginTop: '10px',
    });
    label.textContent = ''
    // 文本域容器
    const textareaWrapper = document.createElement('div');
    textareaWrapper.style.overflowY = 'auto';
    textareaWrapper.style.maxHeight = '100px';
    const textarea = document.createElement('textarea');
    Object.assign(textarea.style, {
        fontSize: '12px',
        paddingLeft: '20px',
        paddingRight: '20px',
        width: '100%',
        border: 'none',
        resize: 'none',
        overflow: 'hidden',
        outline: 'none',
        backgroundColor: 'var(--color-background)',
        pointerEvents: 'none', // 禁用交互
        opacity: '0.7'  // 添加这行来设置透明度
    });
    textarea.tabIndex = -1;  // 禁用键盘聚焦
    textarea.value = '...';
    // 自动高度调整
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // 组装元素
    textareaWrapper.appendChild(textarea);
    container.appendChild(label);
    container.appendChild(textareaWrapper);
    //翻译状态
    let translateStatus = false;
    let isProcessing = false;
    //初始化属性数据
    const initData = () =>{
        translateStatus = false;
        isProcessing = false;
        textarea.value = '...';
        // 手动触发高度调整
        const event = new Event('input', { bubbles: true });
        textarea.dispatchEvent(event);
    }
    // 暴露设置方法
    const setContent = (content) => {
        if (content) {
            textarea.value = content;
            // 手动触发高度调整
            const event = new Event('input', { bubbles: true });
            textarea.dispatchEvent(event);
        }
    };
    const getContent = () => {
        return textarea.value;
    };
    const setTitle = (title) => {
        if (title) label.textContent = title;
    };
    const setTranslateStatus = (status) => {
        translateStatus = status;
    };
    const getTranslateStatus = () => {
        return translateStatus; // 直接返回状态值
    };
    const setIsProcessing = (status) => {
        isProcessing = status;
    };
    const getIsProcessing = () => {
        return isProcessing; // 直接返回状态值
    };

    return {
        initData,
        container,    // DOM节点
        setTitle,   //设置翻译线路
        setContent,    // 设置内容的方法
        setTranslateStatus,//设置翻译状态
        getTranslateStatus,
        getIsProcessing,
        setIsProcessing,
        getContent
    };
};
const styledTextarea = createStyledTextarea()
const addTranslatePreview = ()=>{
    // 更安全的选择器写法
    const msgDiv = document.querySelector('div.message-input-wrapper')
    if (msgDiv) {
        // 创建父容器
        const container = document.createElement('div');
        // 将原元素和新元素包裹进容器
        msgDiv.parentNode.insertBefore(container, msgDiv);
        container.appendChild(styledTextarea.container);
        container.appendChild(msgDiv);
    }
}
const addTranslateListener = () => {
    // 获取元素
    const editableDiv = document.getElementById('editable-message-text');

    // 防抖函数
    const debounce = (func, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId); // 清除之前的定时器
            timeoutId = setTimeout(() => func.apply(this, args), delay); // 设置新的定时器
        };
    };

    // 处理输入事件的逻辑
    const handleInput = async () => {
        const flag = trcConfig.translatePreview;
        const textContent = editableDiv.textContent.trim();
        const sendStatus = trcConfig.sendTranslateStatus;
        if(!textContent) {
            styledTextarea.setContent('...')
        }
        if (flag && flag === 'true' && sendStatus === 'true') {
            const to = trcConfig.sendTargetLanguage;
            styledTextarea.setTranslateStatus(false)
            await sendTranslate(textContent,to);
        }
    };

    // 监听输入事件，使用防抖
    editableDiv.addEventListener('input', debounce(handleInput, 300)); // 延时 300ms
    let isProcessing = false;
    editableDiv.addEventListener('keydown', async (event) => {
        if (event.key === 'Enter' && !event.ctrlKey && !event.shiftKey && !event.altKey && !event.metaKey) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();

            if (isProcessing) return;

            const sendTranslateStatus = trcConfig.sendTranslateStatus;
            const sendPreview = trcConfig.translatePreview;
            const textContent = editableDiv.textContent.trim();
            const from = trcConfig.sendSourceLanguage;
            const to = trcConfig.sendTargetLanguage;
            const route = trcConfig.translateRoute;
            const mode = trcConfig.mode;
            const args = {text:textContent,from:from,to: to,route:route,mode:mode};

            if (sendPreview === 'true'&& sendTranslateStatus === 'true') {
                const status = styledTextarea.getTranslateStatus()
                const isProcess = styledTextarea.getIsProcessing()
                if (status === true && isProcess === false) {
                    const translateText = styledTextarea.getContent();
                    await inputMsg(translateText)
                    sendMsg()
                    styledTextarea.setTranslateStatus(false)
                    styledTextarea.setContent('...')
                    return;
                }else {
                    console.log('正在处理翻译中：')
                    return;
                }
            }else if (textContent && sendTranslateStatus === 'true') {
                isProcessing = true;
                styledTextarea.setContent('翻译中...')
                const res = await ipc.translateText(args)
                if (res.status) {
                    const translateText = res.data;
                    await inputMsg(translateText)
                    styledTextarea.setContent(translateText);
                    setTimeout(()=>{
                        isProcessing = false;
                        sendMsg()
                        styledTextarea.setContent('...');
                    },500)
                }else {
                    styledTextarea.setContent(res.message);
                    isProcessing = false;
                    return;
                }
            }
            const chineseDetectionStatus = trcConfig.chineseDetectionStatus;
            if (chineseDetectionStatus === 'true' && sendTranslateStatus === 'true') {
                const textContent = editableDiv.textContent.trim();
                if (containsChinese(textContent)) {
                    return;
                }
            }
            sendMsg();
        }
    },true);

    // 监听父容器的事件
    document.body.addEventListener('click', function(event) {
        // 通过特征匹配目标元素
        const target = event.target.closest('button[aria-label="Send Message"]');
        if (target && target.classList.contains('send')) {
            const status = styledTextarea.getTranslateStatus()
            const isProcess = styledTextarea.getIsProcessing()
            const sendTranslateStatus = trcConfig.sendTranslateStatus;
            console.log('当前状态：',{
                styStatus:status,
                styIsProcess:isProcess,
                sendTranslateStatus:sendTranslateStatus,
            })
            if (isProcess === true) {
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
            }else {
                if (event.isSimulated === true) {
                    //不做处理
                }else if (sendTranslateStatus === 'true' && status === true) {
                    const translateText = styledTextarea.getContent();
                    inputMsg(translateText).then(()=>{})
                }
            }
        }
    });
};

const updateConfigInfo = async () => {
    const currentUserId = getCurrentUserId();
    const args = {platform: 'Telegram', userId: currentUserId || ''};
    const res = await ipc.getTranslateConfig(args)
    if (res.status) {
        styledTextarea.setTitle(res.data.zhName);
        trcConfig = res.data;
        console.log('获取配置信息：args:',args)
    }else {
        trcConfig = {};
        console.error('获取配置信息失败：',res);
    }
}

//会话列表切换触发函数
const sessionChange = async () => {
    const currentUserId = getCurrentUserId();
    const args = {platform: 'Telegram', userId: currentUserId};
    ipc.infoUpdate(args)
    await updateConfigInfo()
    const myNode = document.getElementById('custom-translate-textarea')
    if (!myNode) {
        addTranslatePreview();
        addTranslateListener();
    }
    styledTextarea.initData()
}
const debouncedSessionChange = debounce(sessionChange,500);

//========================翻译列表处理开始============
const getMsgText = (parentNode) => {
    if (!parentNode) {
        console.error("parentNode is null or undefined");
        return "";
    }

    // 获取 parentNode 下的第一个包含 'text-content' 类的节点
    const textContentNode = parentNode.querySelector('.text-content');
    if (!textContentNode) {
        console.error("没有找到 '.text-content' 类的节点");
        return "";
    }

    let allText = "";
    const textContentNodes = textContentNode.childNodes;
    // 遍历所有子节点，获取并合并文本
    textContentNodes.forEach((node) => {
        if (node.nodeName === "BR") {
            allText += "\n"; // 如果是 <br> 标签，添加换行符
        } else if (node.nodeType === Node.TEXT_NODE) {
            allText += node.textContent; // 获取文本节点的内容
        }
    });
    // 返回合并后的文本字符串
    return allText?.trim();  // 去除结尾多余的换行符
}
const monitorMainNode = ()=> {
    // 监听整个 body 的 DOM 变化，等待 #main 节点的出现
    const observer = new MutationObserver(async (mutationsList, observer) => {
        for (let mutation of mutationsList) {
            if (mutation.type === 'childList') {
                //设置授权码
                // 检查是否已经存在 id="main" 的节点
                const mainNode = document.getElementById('LeftColumn-main');
                if (mainNode) {
                    // 停止对 body 的观察，避免不必要的性能开销
                    observer.disconnect();
                    // 开始监听 #main 节点的子节点变化
                    observePaneSide(mainNode);
                    break;
                }
            }
        }
    });
    // 开始观察 body 的子节点变化
    observer.observe(document.body, { childList: true, subtree: true });
    // 监听会话列表切换
    const observePaneSide = (paneSideNode)=> {
        const observer = new MutationObserver(async (mutationsList) => {
            for (const mutation of mutationsList) {
                // 确保是 class 属性变化事件
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const targetNode = mutation.target;
                    // 检查 class 是否包含 selected
                    if (targetNode.classList.contains("selected")) {
                        observeChatChanges();
                        debouncedSessionChange()
                    }
                }
            }
        });
        // 配置 MutationObserver，监听 class 属性变化
        const config = { attributes: true, subtree: true, attributeFilter: ["class"] };
        // 监听 pane-side 节点
        observer.observe(paneSideNode, config);
    }

    /**
     * 监视聊天窗口的变化
     */
    const observeChatChanges = () => {
        const chatContainer = document.querySelector("#MiddleColumn");
        if (chatContainer) {
            const observer = new MutationObserver((mutations) => {
                observer.disconnect(); // 暂时断开观察器以避免循环触发
                debouncedAddTranslateButtonToAllMessages()
                observer.observe(chatContainer, {
                    childList: true,
                    subtree: true,
                });
            });
            observer.observe(chatContainer, {
                childList: true,
                subtree: true,
            });
        }
    };
    const addTranslateButtonToAllMessages = async () => {
        const messageElements = document.querySelectorAll("div[data-message-id].message-list-item");
        await updateConfigInfo();

        // 从最后一个元素开始循环
        for (let i = messageElements.length - 1; i >= 0; i--) {
            const item = messageElements[i];
            const msgSpan = item.querySelector('div[dir="auto"].text-content.clearfix.with-meta');
            if (msgSpan) {
                const flag = msgSpan.hasAttribute('data-translated');
                if (flag === false) {
                    msgSpan.setAttribute('data-translated', 'yes');
                    await createTranslateButtonForMessage(msgSpan);
                }
            }
        }
    };
    const createTranslateButtonForMessage = async (msgSpan) => {
        let text = getMsgText(msgSpan.parentNode);
        if (!text) return;
        const translateDiv = document.createElement("div");
        translateDiv.style.cssText = `
        min-height: 20px;
        display: flex;`;

        const leftDiv = document.createElement("div");
        leftDiv.style.cssText = `
            min-height: 20px;
            font-size: 13px;
            float: left;
            color: var(--color-text);
            filter: opacity(70%);
        `;

        const rightDiv = document.createElement("div");
        rightDiv.style.cssText = `
            cursor: pointer;
            width: 20px;
            height: 20px;
            float:left;
            user-select: none;
            margin-left:5px;
        `;
        rightDiv.textContent = '🔄'
        rightDiv.addEventListener('click', async (e) => {
            let text = getMsgText(msgSpan.parentNode);
            text = text.replace(/\n/g, '<br>');
            leftDiv.style.color = 'var(--color-text)';
            leftDiv.textContent = `翻译中...`;
            rightDiv.style.display = 'none';
            //发送请求获取翻译结果
            const route = trcConfig.translateRoute;
            const from = trcConfig.receiveSourceLanguage;
            const to = trcConfig.receiveTargetLanguage;
            const mode = trcConfig.mode;
            const res = await ipc.translateText({route: route, text: text,from:from, to: to,refresh:'true',mode:mode});
            if (res.status) {
                leftDiv.innerHTML = res.data;
                rightDiv.style.display = '';
            }else {
                leftDiv.style.color = 'red';
                leftDiv.textContent = `${res.message}`;
                rightDiv.style.display = '';
            }
        });
        // 组装结构
        translateDiv.appendChild(leftDiv);
        translateDiv.appendChild(rightDiv);
        msgSpan.style.borderBottom = '1px dashed var(--color-text)';
        msgSpan.style.paddingBottom = '5px';
        // 插入到消息元素右侧
        msgSpan.parentNode.insertBefore(translateDiv, msgSpan.nextSibling);

        const receiveTranslateStatus = trcConfig.receiveTranslateStatus;
        if (receiveTranslateStatus === 'true') {
            let text = getMsgText(msgSpan.parentNode);
            text = text.replace(/\n/g, '<br>');
            leftDiv.textContent = `翻译中...`;
            rightDiv.style.display = 'none';
            //发送请求获取翻译结果
            const route = trcConfig.translateRoute;
            const from = trcConfig.receiveSourceLanguage;
            const to = trcConfig.receiveTargetLanguage;
            const mode = trcConfig.mode;
            const res = await ipc.translateText({route: route, text: text,from:from, to: to,mode:mode});
            if (res.status) {
                leftDiv.innerHTML = res.data;
                rightDiv.style.display = '';
            }else {
                leftDiv.style.color = 'red';
                leftDiv.textContent = `${res.message}`;
                rightDiv.style.display = '';
            }
        }

    };
    const debouncedAddTranslateButtonToAllMessages = debounce(addTranslateButtonToAllMessages,200);
}
/**
 * 函数防抖，用于优化频繁触发的操作
 * @param {Function} func 需要防抖的函数
 * @param {number} delay 防抖延迟时间（毫秒）
 */
function debounce (func, delay) {
    let timeout;
    return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), delay);
    };
};
monitorMainNode()
//========================翻译列表处理结束============


//快捷回复相关部分
async function inputMsg(translation) {
    // 查找富文本输入框
    const richTextInput = document.getElementById('editable-message-text');
    // 1. 检测操作系统
    const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
    // 2. 聚焦输入框
    richTextInput.focus();

    // 3. 模拟 Ctrl+A/Command+A
    const selectAll = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: !isMac,
        metaKey: isMac,
        bubbles: true
    });
    richTextInput.dispatchEvent(selectAll);
    // 4. 模拟退格键
    const backspace = new KeyboardEvent('keydown', {
        key: 'Backspace',
        code: 'Backspace',
        bubbles: true
    });
    richTextInput.dispatchEvent(backspace);

    richTextInput.innerText = translation;
    // 7. 触发输入事件
    const inputEvent = new InputEvent('input', {
        bubbles: true,
        cancelable: true,
        inputType: 'insertText',
        data: translation
    });
    richTextInput.dispatchEvent(inputEvent);
}

const quickReply = async (args)=>{
    const {type,text} = args;
    if (type === 'input') {
        await inputMsg(text);
    }
    if (type === 'send') {
        await inputMsg(text);
        let sendButton = document.querySelectorAll('button.Button.send.main-button.default.secondary.round.click-allowed')[0]
        if (sendButton) {
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            });
            event.isSimulated = true; // 标记为模拟事件
            sendButton.dispatchEvent(event);
        }
    }
}
