<template>
  <div class="flex items-center justify-between px-3 mt-3">
    <ul class="contact-tab">
      <li v-for="item in tabList" :key="item.label" :class="{ active: modelValue === item.value }" @click="handleTabClick(item.value)">
        {{ item.label }}
      </li>
      <li class="flex-center" :class="{ 'active': modelValue === 'tag' }">
        <el-dropdown popper-class="a2c-dropdown">
          <el-button link>
            <div class="flex items-center max-w-100px">
              <svg-icon v-if="activeTag" icon-class="tag" class-name="!w-5 mr-2 flex-shrink-0" :style="{ color: activeTag?.color }" />
              <span class="text-base font-normal truncate">{{ activeTag?.label || '标签' }}</span>
            </div>
            <el-icon class="text-xl ml-2">
              <ArrowDown />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in tagList" :key="item.value" @click="handleTagClick(item)">
                <div class="flex items-center overflow-hidden">
                  <svg-icon icon-class="tag" class-name="!w-5 mr-2 flex-shrink-0" :style="{ color: item.color }" />
                  <span class="text-base font-normal truncate">{{ item.label }}</span>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ArrowDown } from '@element-plus/icons-vue';

const currentChat = ref({
  staffPlatformType: 1,
  isVirtualSeat: false,
});

const props = defineProps<{
  modelValue: string | number;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number): void;
  (e: 'change', value: string | number): void;
}>();

const tabList = computed(() => {
  if (currentChat.value.staffPlatformType === 1 && !currentChat.value.isVirtualSeat) {
    return [
      {
        label: '所有',
        value: null
      },
      {
        label: '通讯录',
        value: 1
      },
      {
        label: '未读',
        value: 3
      }
    ];
  } else {
    return [
      {
        label: '所有',
        value: null
      },
      {
        label: '未读',
        value: 3
      }
    ];
  }
});

const handleTabClick = (value: string | number) => {
  activeTag.value = null;
  emit('update:modelValue', value);
  emit('change', value);
};


// 标签相关
const activeTag = ref(null);
const tagList = ref([
  {
    label: '标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1标签1',
    value: 'label1',
    color: '#10B981'
  },
  {
    label: '标签2',
    value: 'label2',
    color: '#f80'
  }
]);

const handleTagClick = (item: any) => {
  activeTag.value = item;
  emit('update:modelValue', 'tag');
  emit('change', 'tag');
};
</script>

<style lang="scss" scoped>
.contact-tab {
    @apply flex gap-10px;
    li {
      @apply relative min-w-52px px-10px h-32px leading-32px bg-#F0F2F5 text-#54656F rounded-32px text-center cursor-pointer transition-all duration-300;
      &.active {
        @apply bg-#E7FCE3 text-#10B981;
      }
      .msg-tips {
        @apply absolute top--1 left-70% text-xs leading-none px-1 py-0.5 text-white bg-red-500 rounded-full opacity-90 transform scale-90;
      }
    }
  }
</style>
