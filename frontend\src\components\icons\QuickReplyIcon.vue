<template>
  <svg t="1740504035865" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5036" :width="size" :height="size">
    <path
      d="M661.418667 853.546667A278.272 278.272 0 0 1 512 1002.965333a278.314667 278.314667 0 0 1-149.418667-149.418666h96.426667c13.909333 20.821333 31.872 38.912 52.992 53.034666 21.12-14.08 39.082667-32.213333 52.992-53.034666h96.426667zM768 631.850667l85.333333 96.810666v82.218667H170.666667v-82.218667l85.333333-96.810666V384.213333c0-148.608 106.837333-275.072 256-321.92 149.162667 46.848 256 173.312 256 321.92v247.637334z m-31.146667 93.696L682.666667 664.106667v-279.893334c0-98.901333-66.986667-189.013333-170.666667-231.296-103.68 42.24-170.666667 132.394667-170.666667 231.253334v279.936l-54.186666 61.44h449.706666z m-224.853333-256a85.333333 85.333333 0 1 1 0-170.666667 85.333333 85.333333 0 0 1 0 170.666667z"
      :fill="color"
      p-id="5037"
    ></path>
  </svg>
</template>

<script setup>
defineProps({
  size: [Number, String],
  color: {
    type: String,
    default: 'currentColor' // 默认继承父级颜色
  }
});
</script>
