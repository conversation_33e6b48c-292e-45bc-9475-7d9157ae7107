<template>
  <div class="about-settings w-full h-full">
    <div class="about-header">
      <div class="text-18px font-bold mb-4">关于</div>
      <el-form ref="form" label-position="left" label-width="100px">
        <el-form-item label="设备ID">
          <div class="text-14px text-#10B981">{{ systemInfo?.machineCode }}</div>
          <el-icon class="ml-2 cursor-pointer" size="16" @click="copyMachineCode">
            <CopyDocument />
          </el-icon>
        </el-form-item>
        <el-form-item label="使用帮助">
          <a class="text-14px" href="https://www.yuque.com/u121015/help" target="_blank">产看文档</a>
        </el-form-item>
        <el-form-item label="联系客服">
          <a class="text-14px" href="https://www.yuque.com/u121015/help" target="_blank">@a2c_chat</a>
        </el-form-item>
        <el-form-item label="版本号">
          <span class="text-14px">v {{ systemInfo?.version }}</span>
          <el-icon class="ml-2" size="16" style="color: #10b981">
            <refresh />
          </el-icon>
        </el-form-item>
        <el-form-item label="清理缓存">
          <div class="text-14px">当前缓存文件 {{ cacheInfo.cacheSizeFormatted || '0 B' }}</div>
          <el-button class="ml-2" type="text" @click="clearCache" :loading="clearingCache" :disabled="clearingCache">
            {{ clearingCache ? '清理中...' : '立即清理' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { CopyDocument, Refresh } from '@element-plus/icons-vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';

const systemInfo = ref<any>({});
const cacheInfo = ref<any>({});
const clearingCache = ref(false);

// 初始化时尝试读取本地保存的授权码
onMounted(async () => {
  await initData();
  await loadCacheInfo();
});

const initData = async () => {
  try {
    systemInfo.value = await ipc.invoke(ipcApiRoute.getSystemInfo, {});
    console.log('systemInfo', systemInfo.value);
  } catch (error) {
    ElMessage.error(`初始化失败：${error.message}`);
  }
};

const loadCacheInfo = async () => {
  try {
    const result = await ipc.invoke(ipcApiRoute.getCacheInfo, {});
    if (result.status) {
      cacheInfo.value = result.data;
    }
  } catch (error) {
    console.error('获取缓存信息失败:', error);
  }
};
const copyMachineCode = async () => {
  try {
    await navigator.clipboard.writeText(systemInfo.value?.machineCode);
    ElMessage({
      message: '复制成功',
      type: 'success',
      offset: 40
    });
  } catch (err) {
    ElMessage({
      message: '复制失败',
      type: 'error',
      offset: 40
    });
  }
};
const clearCache = async () => {
  try {
    clearingCache.value = true;
    const result = await ipc.invoke(ipcApiRoute.clearCache, {});

    if (result.status) {
      ElMessage.success({
        message: `缓存文件清理成功！清理了 ${result.data.clearedSizeFormatted}，${result.data.clearedFiles} 个文件`,
        type: 'success',
        offset: 40,
        duration: 3000
      });
      // 重新加载缓存信息
      await loadCacheInfo();
    } else {
      ElMessage.error({
        message: result.message || '缓存清理失败',
        type: 'error',
        offset: 40
      });
    }
  } catch (error) {
    console.error('清理缓存失败:', error);
    ElMessage.error({
      message: '清理缓存失败',
      type: 'error',
      offset: 40
    });
  } finally {
    clearingCache.value = false;
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  @apply text-14px font-bold;
}
</style>
