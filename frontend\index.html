<!doctype html>
<html lang="zh-CN" class="dark">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0" />
    <title>A2C Assistant</title>
    <style>
      #loadingPage {
        background: linear-gradient(135deg, #f8fafc 0%, #e2f8f0 50%, #f8fafc 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .loading-container {
        text-align: center;
      }

      .loading-spinner {
        width: 48px;
        height: 48px;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #10b981;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body style="padding: 0; margin: 0">
    <div id="loadingPage">
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    </div>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
