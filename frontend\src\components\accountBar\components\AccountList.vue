<template>
  <AccountItem v-for="item in accountList" :key="item.id" :item="item" />
</template>

<script lang="ts" setup>
import AccountItem from './AccountItem.vue';
import type { Account } from '@/types/account';

const accountList = ref<Account[]>([
  {
    id: -1,
    talkUserId: -1,
    nickname: 'WhatsApp',
    account: 'API虚拟账号',
    headImg: '',
    useStatus: 1,
    onlineStatus: 1,
    totalUnReadNum: 0,
    type: 1
  },
  {
    id: 16556,
    talkUserId: '1897816298950025218',
    loginEndpoint: 2,
    account: '*************',
    accountJson: null,
    createTime: '2025-07-09 14:40:59',
    headImg: 'https://pub-1fc208598edc41debaecb57eb72b050e.r2.dev/HeadImg*************.png',
    groupType: 1,
    groupId: 763,
    groupName: null,
    nickname: null,
    useStatus: 1,
    seatStatus: 2,
    seatAccount: '5125',
    onlineStatus: 5,
    reason: '账号已失效',
    tagIds: null,
    type: 2,
    country: null,
    accountEnvironment: null,
    registerTime: null,
    firstLoginTime: '2025-07-09 14:41:01',
    offlineTime: '2025-07-21 15:24:17',
    remark: null,
    topStatus: 0,
    isDel: 0,
    fileId: null,
    accountType: 2,
    importType: null,
    delTime: null,
    tags: null,
    totalUnReadNum: 582,
    sleepTime: null,
    nurturingCount: 0,
    broadcastCount: 1,
    broadResetTime: '2025-07-19 10:06:08',
    seatInfoVo: null
  },
  {
    id: 16555,
    talkUserId: '1897816298950025218',
    loginEndpoint: 2,
    account: '************',
    accountJson: null,
    createTime: '2025-07-08 14:52:29',
    headImg: 'https://pub-1fc208598edc41debaecb57eb72b050e.r2.dev/HeadImg************.png',
    groupType: 1,
    groupId: 814,
    groupName: null,
    nickname: null,
    useStatus: 1,
    seatStatus: 2,
    seatAccount: '5125',
    onlineStatus: 1,
    reason: '',
    tagIds: null,
    type: 1,
    country: null,
    accountEnvironment: null,
    registerTime: null,
    firstLoginTime: '2025-07-08 14:52:29',
    offlineTime: '2025-07-21 14:20:00',
    remark: null,
    topStatus: 0,
    isDel: 0,
    fileId: null,
    accountType: 1,
    importType: null,
    delTime: null,
    tags: null,
    totalUnReadNum: 0,
    sleepTime: null,
    nurturingCount: 0,
    broadcastCount: 2,
    broadResetTime: '2025-07-14 16:53:22',
    seatInfoVo: null
  }
]);
</script>

<style lang="scss" scoped></style>
