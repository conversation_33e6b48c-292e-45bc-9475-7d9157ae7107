<script setup>
import { ElIcon } from 'element-plus';
import { InfoFilled } from '@element-plus/icons-vue';
</script>

<template>
  <div class="google-translate">
    <!--    <el-divider content-position="left">基础信息配置</el-divider>-->

    <div class="translate-notice">
      <el-alert title="提示" type="info" :closable="false" class="custom-alert">
        <template #default>
          <div class="alert-content">
            <el-icon :size="20" class="notice-icon">
              <InfoFilled />
            </el-icon>
            <div class="notice-text">
              <!--              <p class="main-text">当前谷歌翻译为网页版 API</p>-->
              <p class="sub-text">谷歌翻译网页版接口</p>
              <p class="hint-text">* 该接口可能不稳定！如无法使用请使用其它翻译服务！</p>
            </div>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<style scoped lang="scss">
.google-translate {
  height: 200px;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);

  .translate-notice {
    padding: 0 24px;
    margin-top: 16px;

    .custom-alert {
      border-radius: 12px;
      background-color: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-8);

      :deep(.el-alert__title) {
        font-size: var(--el-font-size-base);
        color: var(--el-text-color-primary);
        font-weight: 600;
      }

      .alert-content {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 8px 0;

        .notice-icon {
          color: var(--el-color-primary);
          flex-shrink: 0;
          margin-top: 2px;
          font-size: 18px;
        }

        .notice-text {
          .main-text {
            font-size: var(--el-font-size-base);
            color: var(--el-text-color-primary);
            margin: 0 0 6px 0;
            font-weight: 500;
          }

          .sub-text {
            font-size: var(--el-font-size-extra-small);
            color: var(--el-text-color-regular);
            margin: 0 0 4px 0;
            line-height: 1.4;
          }

          .hint-text {
            font-size: var(--el-font-size-extra-small);
            color: var(--el-text-color-secondary);
            margin: 6px 0 0 0;
            font-style: italic;
          }
        }
      }
    }
  }
}
</style>
