<template>
  <div class="account-item" :class="[{ offline: item.onlineStatus != 1 }, { active: currentChat?.account === item.account }, { 'virtual-account': item.id == -1 }, { 'tiny-item': isFold }]">
    <div class="w-full flex justify-between items-center absolute top-5 left--2px right-0 z-1 h-0 pr-2">
      <i class="status-online" :class="{ 'status-online-web': item.loginEndpoint === 2, 'status-online-api': item.id == -1, 'grayscale opacity-50': item.onlineStatus != 1 }">{{ statusText(item) }}</i>
      <div v-if="item.id != -1" class="account-tools">
        <i class="iconfont icon-zhiding"></i>
        <i class="iconfont icon-shuaxin1" :class="{ 'is-loading': item.isRefreshing }" @click.stop="refreshAccount(item)"></i>
        <el-dropdown ref="dropdownRef" popper-class="no-arrow" @command="(command) => accountOperation(command, item)" @visible-change="(visible) => visible && handleDropdownShow(item)">
          <i class="iconfont icon-xuanxiang-chuizhi"></i>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="tool in accountTools" :key="tool.command" :command="tool.command" :disabled="tool.disabled">
                {{ tool.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <dl class="account-info" @click="accountClick(item)">
      <dt class="mr-2" :class="{ grayscale: item.onlineStatus != 1 }">
        <el-badge :value="item.id == -1 ? 0 : 0" :show-zero="false" :max="99" :offset="[-2, 3]">
          <el-avatar v-if="item.headImg && item.id != -1" :size="36" :src="`${item.headImg}?${Math.random().toString(36).substring(2, 15)}`" />
          <svg-icon v-else-if="item.id == -1" icon-class="whatsappBg" class-name="!w-9 !h-9 text-#65CF72" />
          <span v-else class="flex items-center justify-center w-9 h-9 text-20px text-#fff bg-green-500 rounded-full">{{ item.account.charAt(0) }}</span>
        </el-badge>
      </dt>
      <dd class="flex-1 overflow-hidden">
        <p class="account-nickname">
          <span>{{ item.nickname || '-' }}</span>
          <template v-if="userStore.vipInfo.memberType == 3 && item.id == -1">
            <svg-icon icon-class="api"></svg-icon>
          </template>
        </p>
        <p v-if="item.id != -1" class="account-number mt-2">
          {{ item.account }}
        </p>
        <p v-else-if="isFold" class="account-api"><svg-icon icon-class="api" class-name="!w-5 !h-5"></svg-icon></p>
      </dd>
    </dl>
    <p v-if="item.id != -1" class="account-remark mt-1 flex items-center gap-2 pl-3 pr-3">
      <el-tooltip v-if="item.remark" :content="item.remark" effect="light">
        <span class="whitespace-nowrap overflow-hidden text-ellipsis">备注：{{ item.remark || '-' }}</span>
      </el-tooltip>
      <span v-else class="whitespace-nowrap overflow-hidden text-ellipsis">备注：-</span>
      <el-icon size="12" class="cursor-pointer" @click="editRemark(item)"><Edit /></el-icon>
    </p>
    <p v-if="item.id != -1 && item.reason && item.onlineStatus != 1" class="account-reason mt-1 pl-3 pr-3">原因：{{ item.reason || '-' }}</p>
    <p v-if="item.id != -1 && item.ip" class="account-ip mt-2 pl-3 pr-3">IP：{{ item.ip || '-' }}</p>
    <i v-if="userStore.vipInfo.memberType == 3 && item.id == -1" class="color-border absolute"></i>

    <!-- 鼠标悬浮时显示的账号操作 -->
    <div class="account-operation">
      <el-tooltip content="打开账号" placement="top">
        <el-button circle size="large">
          <i class="iconfont icon-cundanghuihuaguanli"></i>
        </el-button>
      </el-tooltip>
      <el-tooltip v-if="item.id != -1" content="环境配置" placement="top">
        <el-button circle size="large">
          <i class="iconfont icon-fuwuqi"></i>
        </el-button>
      </el-tooltip>
      <el-tooltip v-if="item.id != -1" content="删除账号" placement="top">
        <el-button circle size="large">
          <i class="iconfont icon-shanchu"></i>
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue';
import type { DropdownInstance } from 'element-plus';
import { useClipboard } from '@vueuse/core';
import type { Account } from '@/types/account';
import { useAccountStore } from '@/stores/modules/account';

const accountStore = useAccountStore();
const { isFold } = storeToRefs(accountStore);

// demo数据
const userStore = ref({
  vipInfo: {
    memberType: 3
  }
});

const currentChat = ref({
  account: '*************',
  mode: 1
});

const { copy } = useClipboard({ legacy: true });

const props = defineProps({
  item: {
    type: Object as PropType<Account>,
    default: () => ({})
  }
});

const loginEndpointObj = reactive({
  1: '安卓',
  2: 'Web',
  0: ''
});
const statusText = (item: Account) => {
  if (item.id === -1) {
    return 'API';
  }
  if (item.reason === '账号已经封号') {
    return loginEndpointObj[item.loginEndpoint || 0] + '封号';
  } else if (item.onlineStatus === 1) {
    return loginEndpointObj[item.loginEndpoint || 0] + '在线';
  } else {
    return loginEndpointObj[item.loginEndpoint || 0] + '离线';
  }
};

const emit = defineEmits(['get-list', 'show-online-dialog']);
// 账号点击
const accountClick = async (item: Account) => {
  if (currentChat.value.account == item.account) return;
};
// 修改资料弹出
const modifyDialogRef = ref(null);
// 账号继承弹出
const inheritDialogRef = ref(null);
// 账号操作
const dropdownRef = ref<DropdownInstance>(null);
const accountTools = computed(() => {
  return [
    {
      label: '账号上线',
      command: 'online'
    },
    {
      label: '账号下线',
      command: 'offline',
      disabled: false
    },
    {
      label: '修改资料',
      command: 'modify',
      disabled: false
    },
    {
      label: '导出粉丝',
      command: 'export'
    },
    {
      label: '释放IP',
      command: 'releaseIp'
    },
    {
      label: '一键清空会话',
      command: 'clear',
      disabled: false
    },
    {
      label: '复制账号链接',
      command: 'copylink',
      disabled: false
    },
    {
      label: '置顶',
      command: 'top',
      disabled: false
    }
  ];
});
const accountOperation = async (command: string, item: Account) => {
  if (command === 'top') {
    // 置顶 type 0取消置顶 1置顶
  }
  if (command === 'online') {
    emit('show-online-dialog', [{ account: item.account, onlineStatus: item.onlineStatus, useStatus: item.useStatus }]);
  }
  if (command === 'offline') {
    // 显示下线弹窗
    selectedAccounts.value = [item.account];
    showOfflineDialog.value = true;
  }
  if (command === 'modify') {
    // 修改资料
    // const ids = [item.account];
    modifyDialogRef.value.open(item);
    // router.push(`/account/modify/edit?ids=${ids}`);
  }
  if (command === 'export') {
    // 导出粉丝
    console.log(item);
    ElMessageBox.confirm('是否确定导出该账号的粉丝？', '操作提醒', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const query = {
        seatAccount: item.seatAccount,
        account: item.account
      };
    });
  }
  if (command === 'releaseIp') {
    // 释放IP
    ElMessageBox.confirm('是否确定释放所选择的账号IP？', '操作提醒', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {});
  }
  if (command === 'clear') {
    // 一键清空会话
    ElMessageBox.confirm('一键清空好友会话后，重新发送首条消息会有延迟时间（后续发送消息正常），是否仍要执行？', '确定要一键清空会话吗？', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {});
  }
  if (command === 'copylink') {
    // 复制账号链接
  }
  if (command === 'inheritance') {
    // 判断当前账号是否在线 onlineStatus === 1
    if (item.onlineStatus !== 1) {
      ElMessage.error('当前账号不在线，无法进行继承操作');
      return;
    }
    // 账号继承
  }
};

// 刷新账号
const refreshAccount = (item: Account) => {};
// 添加 dropdown 显示时的处理函数
const handleDropdownShow = (item: Account) => {
  const index = accountTools.value.findIndex((tool) => tool.command === 'top');
  if (index > -1) {
    accountTools.value[index].label = item.topStatus ? '取消置顶' : '置顶';
  }
};
// 下线弹窗相关
const showOfflineDialog = ref(false);
const offlineLoading = ref(false);
const selectedAccounts = ref<string[]>([]);
// 编辑备注
const editRemark = (row: Account) => {
  ElMessageBox.prompt('', '编辑备注', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入备注',
    inputValue: row.remark,
    inputType: 'textarea',
    inputErrorMessage: '请输入1-30个字符',
    inputValidator: (value: string) => {
      if (value === null || value.length > 30) {
        return '请输入1-30个字符';
      }
      return true;
    }
  }).then(({ value }) => {});
};
</script>
<style lang="scss" scoped>
.account-item {
  // width: calc(100% - 8px);
  @apply relative z-1 min-h-80px mb-10px ml-4px pb-13px;

  &::after {
    content: '';
    @apply absolute top-0 left-0 w-full h-full border-1px border-solid border-#f2f3f5 rounded-4px pointer-events-none box-border;
    transition: all 0.1s ease;
  }
  &.active {
    background: #e8fff7;
    &:hover {
      .account-operation {
        display: none;
      }
    }
    &::after {
      content: '';
      border: 2px solid #10b981;
    }
  }
  &.virtual-account.active {
    background: transparent;
    &::after {
      content: '';
      border: none; /* 移除原有边框，使用渐变边框 */
    }
  }
  &:hover {
    .account-operation {
      display: flex;
    }
    // &::after {
    //   content: '';
    //   border: 1px solid #10b981;
    // }
  }
  .status-online {
    @apply inline-block text-xs text-white px-2 pt-1 pb-2 leading-none;
    background-image: url(@/assets/icons/png/online.png);
    background-size: 100% 100%;
    &.status-online-web {
      color: #111;
      background-image: url(@/assets/icons/png/online-web.png);
    }
    &.status-online-api {
      color: #fff;
      background-image: url(@/assets/icons/png/online-api.png);
    }
  }

  .account-tools {
    @apply flex items-center gap-1;
  }

  &.offline {
    .status-online {
      color: #4e596d;
      background-image: url(@/assets/icons/png/offline.png);
    }
  }
  &.virtual-account {
    &:hover {
      &::after {
        display: none;
      }
    }
  }

  .iconfont {
    @apply cursor-pointer;
    &:hover {
      color: var(--el-color-primary);
    }
  }

  // 账号信息
  .account-info {
    @apply flex items-center cursor-pointer px-3 pt-5 pb-1;
  }

  // 账号昵称
  .account-nickname {
    @apply text-sm font-600 whitespace-nowrap overflow-hidden text-ellipsis flex items-center gap-1 leading-none;
  }

  // 账号api
  .account-api {
    @apply flex items-center justify-center;
  }

  // 账号信息
  .account-number,
  .account-reason,
  .account-ip {
    @apply text-xs text-#6B7785 whitespace-nowrap overflow-hidden text-ellipsis leading-none;
  }

  // 账号备注
  .account-remark {
    @apply text-xs text-#6B7785 leading-none mt-1 flex items-center gap-2 pl-3 pr-3;
  }

  // 收缩模式
  &.tiny-item {
    @apply min-h-40px pb-2;
    .status-online {
      @apply absolute top-4 left-20% text-0px w-3 h-3 rounded-full p-0 mr-0 border-2 border-solid border-white;
      background: #10b981;
    }
    .account-tools,
    .account-nickname,
    .account-remark,
    .account-reason,
    .account-ip {
      display: none;
    }

    .account-info {
      @apply pt-3 flex-col overflow-hidden;
      .mr-2 {
        margin-right: 0;
      }
      dd {
        width: 100%;
      }
      .account-number {
        @apply truncate mt-1;
      }
    }

    .account-operation {
      & > :not(:first-child) {
        display: none;
      }
    }
  }

  // 鼠标悬浮时显示的账号操作
  .account-operation {
    @apply absolute top-0 left-0 w-full h-full hidden items-center justify-center bg-black/50 rounded-4px z-2;
    .el-button {
      @apply border-none;
    }
  }
}

.virtual-account {
  .color-border {
    display: block;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(to right, #eee, #eee);
    z-index: -1;
    border-radius: 4px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      inset: 1px;
      background: #fff;
      border-radius: 4px;
    }
  }
  &:hover {
    .color-border {
      background-image: linear-gradient(to right, #2f40e9, #7028d0, #dd02a5, #f99d1e);
    }
  }
  &.active {
    .color-border {
      background-image: linear-gradient(to right, #2f40e9, #7028d0, #dd02a5, #f99d1e);
      &::before {
        background: #e8fff7;
        inset: 2px;
      }
    }
  }
  .status-online {
    display: none;
  }
}
</style>
