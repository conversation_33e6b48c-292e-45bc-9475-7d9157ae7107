const { app } = require('electron');

// 生成随机字符串的纯函数
function generateRandomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return Array.from({ length }, () => {
        const randomIndex = Math.floor(Math.random() * chars.length);
        return chars[randomIndex];
    }).join('');
}

// 生成唯一分区ID的主函数
async function generateUniquePartitionId(options = {}) {
    const {
        length = 8,
        maxRetry = 10,
        tableName = 'session_list',
        idField = 'partitionId'
    } = options;

    let retryCount = 0;

    while (retryCount < maxRetry) {
        const partitionId = generateRandomString(length);

        try {
            const existing = await app.sdb.selectOne(tableName, { [idField]: partitionId });
            if (!existing) return partitionId;
            retryCount++;
        } catch (error) {
            throw new Error(`Database query failed: ${error.message}`);
        }
    }

    throw new Error(`Failed to generate unique ID after ${maxRetry} attempts`);
}

// 获取当前时间字符串
const getTimeStr = (date = new Date())=> {
    const pad = n => n.toString().padStart(2, '0')
    return `${date.getFullYear()}-${pad(date.getMonth()+1)}-${pad(date.getDate())} ` +
        `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
}
const timestampToString = (timestamp) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
// 导出函数
module.exports = {
    timestampToString,
    generateUniquePartitionId,
    getTimeStr,
    generateRandomString // 可选导出基础生成函数
};
