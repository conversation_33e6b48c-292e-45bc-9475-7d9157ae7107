<template>
  <div class="h-full flex flex-col bg-white">
    <!-- 顶部Logo -->
    <div class="flex justify-center py-4 flex-shrink-0">
      <div class="w-9.75 flex items-center justify-center">
        <img alt="logo" class="w-full h-full" src="@/assets/images/logo.svg" />
      </div>
    </div>

    <div class="h-full flex-1 flex flex-col justify-between">
      <!-- 中间导航图标 -->
      <div class="flex flex-col items-center space-y-6 flex-1 py-6">
        <div :class="{ active: activeKey === 'dashboard' }" class="nav-icon text-#10B981" @click="handleNavSelect('dashboard')">
          <i v-if="activeKey === 'dashboard'" class="iconfont icon-a-shouyetianchong"></i>
          <i v-else class="iconfont icon-shouye2"></i>
        </div>
        <div :class="{ active: activeKey === 'whatsApp' }" class="nav-icon text-#10B981" @click="handleNavSelect('whatsApp')">
          <i v-if="activeKey === 'whatsApp'" class="iconfont icon-whatsapp-fill"></i>
          <i v-else class="iconfont icon-whatsapp"></i>
        </div>
        <div :class="{ active: activeKey === 'telegram' }" class="nav-icon text-#3390ec" @click="handleNavSelect('telegram')">
          <i v-if="activeKey === 'telegram'" class="iconfont icon-telegram"></i>
          <i v-else class="iconfont icon-telegram1"></i>
        </div>
      </div>

      <!-- 底部图标 -->
      <div class="flex flex-col items-center space-y-6 py-6 pb-8 flex-shrink-0">
        <div :class="{ active: activeKey === 'settings' }" class="nav-icon text-#6698ff" @click="handleNavSelect('settings')">
          <i v-if="activeKey === 'settings'" class="iconfont icon-shezhi01-F"></i>
          <i v-else class="iconfont icon-shezhi"></i>
        </div>
        <div class="nav-icon text-#ff7575" @click="logOut">
          <i class="iconfont icon-quit"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';

// 定义属性
defineProps<{
  collapsed: boolean;
}>();

const router = useRouter();
const route = useRoute();

// 当前选中的菜单
const activeKey = ref<string>((route.name as string) || 'dashboard');

// 监听路由变化，同步更新左侧栏状态
watch(
  () => route.name,
  (newRouteName) => {
    if (newRouteName && typeof newRouteName === 'string') {
      activeKey.value = newRouteName;
    }
  },
  { immediate: true }
);

// 菜单选择处理
const handleNavSelect = (key: string) => {
  activeKey.value = key;
  router.push({ name: key });
};

const logOut = () => {
  // 只清除 token，保留所有密钥和自动登录状态
  localStorage.removeItem('access_token');

  // 跳转到登录页
  router.push('/login');
  console.log('退出登录完成，已保留自动登录状态');
};
</script>

<style lang="scss" scoped>
.nav-icon {
  width: 100%;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
  position: relative;

  &.active {
    background-color: #f0f2f5;

    &::after {
      content: '';
      position: absolute;
      right: 0;
      height: 100%;
      width: 3px;
      background-color: #22c55e;
      border-radius: 0 3px 3px 0;
    }
  }

  &:hover {
    background-color: #f3f4f6;
  }

  i {
    font-size: 20px;
    font-style: normal;
  }
}
</style>
