<template>
  <div class="user-info">
    <div class="header-container">
      <div class="header-title">
        <el-text tag="b" size="large">{{ t('userInfo.title') }}</el-text>
      </div>
    </div>

    <div class="content-container-input">
      <div class="content-left">
        <el-text>{{ t('userInfo.phoneNumber') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :disabled="true" v-model="userInfo.phoneNumber"></el-input>
      </div>
    </div>

    <div class="content-container-input">
      <div class="content-left">
        <el-text>{{ t('userInfo.nickname') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :disabled="hasUserId" :placeholder="t('userInfo.nickname')" v-model="userInfo.nickName"></el-input>
      </div>
    </div>

    <div class="content-container-input">
      <div class="content-left">
        <el-text>{{ t('userInfo.country') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :disabled="hasUserId" :placeholder="t('userInfo.country')" v-model="userInfo.country"></el-input>
      </div>
    </div>

    <div class="content-container-radio">
      <div class="content-left">
        <el-text>{{ t('userInfo.gender') }}</el-text>
      </div>
      <div class="content-right">
        <el-radio-group :disabled="hasUserId" v-model="userInfo.gender">
          <el-radio value="man">{{ t('userInfo.male') }}</el-radio>
          <el-radio value="woman">{{ t('userInfo.female') }}</el-radio>
        </el-radio-group>
      </div>
    </div>
    <el-divider />
    <div class="header-container">
      <el-text class="header-title" tag="b" size="large">{{ t('userInfo.salesInfo') }}</el-text>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('userInfo.tradeActivity') }}</el-text>
      </div>
      <div class="content-right">
        <el-select :disabled="hasUserId" v-model="userInfo.gradeActivity" :placeholder="t('userInfo.selectPlaceholder')" size="default" style="width: 100%">
          <el-option v-for="item in activityArr" :key="item.id" :label="t(`userInfo.activityStatus.${item.name}`)" :value="item.id" />
        </el-select>
      </div>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('userInfo.customerLevel') }}</el-text>
      </div>
      <div class="content-right">
        <el-select :disabled="hasUserId" v-model="userInfo.customLevel" :placeholder="t('userInfo.selectPlaceholder')" size="default" style="width: 100%">
          <el-option v-for="item in levelArr" :key="item.id" :label="t(`userInfo.customerLevels.${item.name}`)" :value="item.id" />
        </el-select>
      </div>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('userInfo.remarks') }}</el-text>
      </div>
      <div class="content-right">
        <el-input :disabled="hasUserId" type="textarea" v-model="userInfo.remarks" :placeholder="t('userInfo.enterRemarks')" size="default" style="width: 100%"> </el-input>
      </div>
    </div>

    <el-divider />

    <div class="header-container">
      <div class="header-title">
        <el-text tag="b" size="large">{{ t('userInfo.followUpRecords') }}</el-text>
      </div>
      <div class="header-icon-add">
        <el-icon @click="handleAdd">
          <CirclePlus />
        </el-icon>
      </div>
    </div>

    <div class="content-container-scroll">
      <el-scrollbar>
        <el-timeline placement="bottom" style="width: 220px">
          <el-timeline-item v-for="(activity, index) in sortedFollowRecords" :key="activity.id">
            <div class="scroll-content">
              <div class="content-input">
                <div class="input-top">
                  <el-input @focus="handleFocus(activity)" @blur="handleBlur(activity)" v-model="activity.content" type="textarea" :autosize="{ minRows: 1, maxRows: 6 }" />
                </div>
                <div class="input-bottom">
                  <el-text type="info" size="small">{{ activity.timestamp }}</el-text>
                </div>
              </div>
              <div class="content-icon">
                <div v-show="activity.id !== ''" class="delete-icon">
                  <el-icon @click="handleDelete(activity)">
                    <Delete />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup>
import CleanIcon from '@/components/icons/CleanIcon.vue';
import { computed, onMounted, onUnmounted, ref, unref, watch } from 'vue';
import { Check, CirclePlus, Delete, Edit } from '@element-plus/icons-vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import { useMenuStore } from '@/stores/menuStore';
import { useI18n } from 'vue-i18n';

const menuStore = useMenuStore();
const { t } = useI18n();
// 获取当前时间字符串
const getTimeStr = (date = new Date()) => {
  const pad = (n) => n.toString().padStart(2, '0');
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` + `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
};
const userInfo = ref({});

watch(
  () => menuStore.currentPartitionId,
  async (newValue, oldValue) => {
    if (newValue) {
      await getUserInfo();
    }
  }
);

const hasUserId = computed(() => {
  return !userInfo.value.id;
});
// 添加计算属性
const sortedFollowRecords = computed(() => {
  // 创建新数组避免修改原数组
  return [...followRecordArr.value].sort((a, b) => {
    // 升序排列：a.id - b.id
    // 降序排列：b.id - a.id （假设 id 是数字）
    return b.id - a.id;
  });
});
// 定义需要监听的字段
const propertiesToWatch = ['phoneNumber', 'nickName', 'country', 'gender', 'gradeActivity', 'customLevel', 'remarks'];
let watchers = []; // 存储所有字段的监听器
// 初始化字段监听逻辑
const addWatchers = () => {
  removeWatchers(); // 确保不会重复绑定监听器
  watchers = propertiesToWatch.map((property) =>
    watch(
      () => unref(userInfo.value[property]),
      (newValue, oldValue) => {
        if (newValue !== '' && newValue !== oldValue) {
          handlePropertyChange(property, newValue);
        }
      }
    )
  );
};
// 自定义逻辑
const handlePropertyChange = async (property, value) => {
  if (userInfo && userInfo.value?.id) {
    const args = { key: property, value: value, id: userInfo.value.id };
    await ipc.invoke(ipcApiRoute.updateContactInfo, args);
  }
};
// 移除所有字段的监听器
const removeWatchers = () => {
  watchers.forEach((stopWatcher) => stopWatcher()); // 调用每个监听器的停止方法
  watchers = [];
};
const followRecordArr = ref([]);
const getUserInfo = async () => {
  removeWatchers();
  try {
    const args = {
      platform: menuStore.platform,
      userId: '',
      partitionId: menuStore.currentPartitionId
    };
    const res = await ipc.invoke(ipcApiRoute.getContactInfo, args);
    if (res.status) {
      Object.assign(userInfo.value, res.data.userInfo); // 更新表单数据
      Object.assign(followRecordArr.value, res.data.records); // 更新表单数据
    } else {
      userInfo.value = {};
      followRecordArr.value = [];
    }
  } catch (err) {
    console.error('获取配置失败：', error.message);
  } finally {
    addWatchers();
  }
};
onMounted(async () => {
  await getUserInfo();
});
// 生命周期
onMounted(async () => {
  await ipc.removeAllListeners('contact-data-update');
  await ipc.on('contact-data-update', (event, args) => {
    const { data } = args;
    userInfo.value = data.userInfo;
    followRecordArr.value = data.records;
  });
});

onUnmounted(() => {
  ipc.removeAllListeners('contact-data-update');
});
const activityArr = ref([
  { id: 1, name: 'negotiating' },
  { id: 2, name: 'scheduled' },
  { id: 3, name: 'ordered' },
  { id: 4, name: 'paid' },
  { id: 5, name: 'shipped' }
]);
const levelArr = ref([
  { id: 1, name: 'normal' },
  { id: 2, name: 'medium' },
  { id: 3, name: 'important' },
  { id: 4, name: 'critical' }
]);
const handleFocus = (activity) => {};
const handleBlur = async (activity) => {
  const args = { id: activity.id, content: activity.content };
  const res = await ipc.invoke(ipcApiRoute.updateFollowRecord, args);
};
const handleAdd = async () => {
  if (!hasUserId) return;
  const data = {
    userId: userInfo.value.userId,
    platform: menuStore.platform,
    content: '',
    timestamp: getTimeStr()
  };
  const res = await ipc.invoke(ipcApiRoute.addFollowRecord, data);
  if (res.status) {
    followRecordArr.value.unshift(res.data);
  }
};
const handleDelete = async (activity) => {
  const index = followRecordArr.value.indexOf(activity);
  if (index > -1) {
    const args = {
      id: activity.id,
      userId: userInfo.value.id,
      platform: menuStore.platform
    };
    const res = await ipc.invoke(ipcApiRoute.deleteFollowRecord, args);
    if (res.status) {
      followRecordArr.value.splice(index, 1);
    }
  }
};
</script>
<style scoped lang="scss">
.user-info {
  width: 300px;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  :deep(.el-text) {
    --el-text-color: var(--el-text-color-primary);
  }
  :deep(.el-divider--horizontal) {
    margin: 14px 0;
  }
  .header-container {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    user-select: none;
    justify-content: flex-start;

    .header-title {
      display: flex;
      flex: 1;
      height: 30px;
    }
    .header-icon {
      height: 30px;
      display: flex;
      flex: 1;
    }
    .header-icon-add {
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 1;
      cursor: pointer;
    }
  }
  .content-container-select {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    :deep(.el-select__wrapper) {
      border-radius: 0;
    }
    :deep(.el-textarea__inner) {
      border-radius: 0;
    }
    .content-left {
      height: 50px;
      display: flex;
      align-items: center;
      flex: 1;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      align-items: center;
      flex: 2;
    }
  }
  .content-container-input {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    :deep(.el-input__wrapper) {
      border-radius: 0;
    }
    .content-left {
      height: 50px;
      display: flex;
      flex: 1;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 2;
      align-items: center;
    }
  }
  .content-container-radio {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    .content-left {
      height: 50px;
      align-items: center;
      display: flex;
      flex: 1;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 2;
      align-items: center;
    }
  }
  .content-container-scroll {
    height: calc(100% - 530px);
    width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    :deep(.el-timeline-item__wrapper) {
      top: 0;
      right: 15px;
    }
    .scroll-content {
      width: 100%;
      min-height: 50px;
      display: flex;
      :deep(.el-text) {
        --el-text-color: var(--el-color-info);
      }
      .content-input {
        :deep(.el-textarea__inner) {
          border-radius: 0;
        }
        min-height: 50px;
        display: flex;
        flex: 4;
        flex-direction: column; /* 垂直排列 */
        .input-top {
          display: flex;
          flex: 1;
        }
        .input-bottom {
          display: flex;
          flex: 1;
        }
      }
      .content-icon {
        height: 100%;
        width: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column; /* 垂直排列 */
        .add-icon {
          cursor: pointer;
          display: flex;
          align-items: end;
          flex: 1;
        }
        .delete-icon {
          cursor: pointer;
          display: flex;
          align-items: start;
          flex: 1;
        }
        //background-color: #343534;
      }
    }
  }
}
</style>
