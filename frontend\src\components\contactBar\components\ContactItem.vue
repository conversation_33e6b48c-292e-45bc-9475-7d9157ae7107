<template>
  <div class="flex-between contact-item">
    <el-avatar v-if="item.headImg" :size="38" :src="`${item.headImg}`" class="flex-shrink-0" />
      <tk-avatar v-else-if="chatStore.isVirtualSeat" :account="item.friendAccount" />
      <span v-else class="icon-avatar"></span>
      <dl class="flex flex-col w-full flex-1 overflow-hidden" @click.stop="contactItemClick(item)">
        <dt class="flex items-center justify-between mb-1">
          <span class="flex-1 line-clamp-1" :title="accountEncrypt(item.friendAccount)">
            <i v-if="item.isDuplicateFans" class="text-xs text-orange ml-1 vertical-2px">[重粉]</i>
            {{ accountEncrypt(item.friendAccount) }}
            <i v-if="item.visitorStatus >= 0" class="text-xs ml-1 vertical-2px text-gray" :class="{ 'theme-color': item.visitorStatus === 1 }">{{ item.visitorStatus === 1 ? '在线' : item.visitorStatus === 0 ? '离线' : '' }}</i>
          </span>
          <span class="text-gray-500 text-xs flex-shrink-0" :class="{ 'theme-color': Number(item.unReadNum) > 0 }">
            {{ item.messageVO?.createdAt ? formatChatTime(item.messageVO.createdAt) : '' }}
          </span>
        </dt>
        <dd v-if="item.nickname" class="text-green-800 text-xs flex items-center justify-between">称呼：{{ item.nickname }}</dd>
        <dd class="text-xs text-gray-500 mt-1 flex items-center justify-between">
          <p class="flex items-center flex-1">
            <i v-if="chatStore.currentChat.staffPlatformType === 1 && item.messageVO && item.messageVO?.senderAccount != item.friendAccount" class="mr-1 flex-shrink-0" :class="item.messageVO?.receiverRead == 1 ? 'icon-dbcheck' : 'icon-check'"></i>
            <span class="pr-2 line-clamp-1">{{ messageTypeTips(item.messageVO) }}</span>
          </p>
          <span v-if="item.unReadNum > 0" class="new-tips flex-shrink-0">{{ formatUnreadCount(item.unReadNum) }}</span>
        </dd>
      </dl>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { formatChatTime } from '@/utils/dateFormat';
import { formatUnreadCount, messageTypeTips } from '@/utils';
import { Contact } from '@/types/contact';

const chatStore = ref({
    staffPlatformType: 1,
    isVirtualSeat: false,
    currentChat: {
      staffPlatformType: 1,
    }
});

const props = defineProps<{
  item: Contact;
}>();

const contactItemClick = (item: Contact) => {
  console.log(item);
};

const accountEncrypt = (account: string) => {
  return account;
};
</script>

<style lang="scss" scoped>
.contact-item {
    display: flex;
    gap: 10px;
    padding: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: background-color 0.3s ease;
    .new-tips {
      padding: 0 4px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      font-size: 12px;
      color: #fff;
      background: #f53f3f;
      border-radius: 8px;
      transform: scale(0.9);
    }
    &:hover {
      background-color: #f8faf9;
    }
    &.active {
      background-color: #f0f2f5;
    }
    & > * {
      flex-shrink: 0;
      line-height: 1;
    }
  }
</style>