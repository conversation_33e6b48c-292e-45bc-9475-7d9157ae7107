export interface Account {
  id: number
  talkUserId: number | string
  nickname: string | null
  account: string
  headImg: string
  useStatus: number
  onlineStatus: number
  totalUnReadNum: number
  type: number
  loginEndpoint?: number
  accountJson?: any
  createTime?: string
  groupType?: number
  groupId?: number
  groupName?: string | null
  seatStatus?: number
  seatAccount?: string
  reason?: string
  tagIds?: any
  country?: string | null
  accountEnvironment?: any
  registerTime?: string | null
  firstLoginTime?: string
  offlineTime?: string
  remark?: any
  topStatus?: number
  isDel?: number
  fileId?: any
  accountType?: number
  importType?: any
  delTime?: any
  tags?: any
  sleepTime?: any
  nurturingCount?: number
  broadcastCount?: number
  broadResetTime?: string
  seatInfoVo?: any
  isRefreshing?: boolean
  ip?: string
}
