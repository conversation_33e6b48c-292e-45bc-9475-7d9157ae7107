<template>
  <div class="system-settings w-full h-full">
    <div class="about-header">
      <div class="text-18px font-bold mb-4">系统设置</div>
      <el-form ref="formRef" :model="form">
        <div class="font-size-3.5 text-#595959">缓存目录(更改后自动重启)</div>
        <el-form-item>
          <div class="flex w-full mt">
            <el-button type="default" @click="selectCacheDirectory">选择更改目录</el-button>
            <el-input v-model="cacheAddress" style="width: 80%; margin-left: 10px" readonly></el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.powerBoot"> 开机自启 </el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.minimizeToTray"> 关闭窗口最小化到任务栏 </el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.trayMessage"> 系统托盘上显示未读消息 </el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.messagePrompt"> 系统消息提示 </el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.promptSound"> 系统消息提示声音 </el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.pageZoom"> 页面缩放 </el-checkbox>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';

const cacheAddress = ref('');
const form = ref({
  powerBoot: false,
  minimizeToTray: false,
  trayMessage: false,
  messagePrompt: false,
  promptSound: false,
  pageZoom: false
});
const formRef = ref(null);

// 初始化数据
onMounted(async () => {
  await loadSystemSettings();
  await loadCacheDirectory();
});

// 加载系统设置
const loadSystemSettings = async () => {
  try {
    const result = await ipc.invoke(ipcApiRoute.getSystemSettings, {});
    if (result.status) {
      Object.assign(form.value, result.data);
    } else {
      ElMessage.error(result.message || '加载系统设置失败');
    }
  } catch (error) {
    ElMessage.error('加载系统设置失败');
    console.error('加载系统设置失败:', error);
  }
};

// 加载缓存目录
const loadCacheDirectory = async () => {
  try {
    const result = await ipc.invoke(ipcApiRoute.getCacheDirectory, {});
    if (result.status) {
      cacheAddress.value = result.data;
    }
  } catch (error) {
    console.error('加载缓存目录失败:', error);
  }
};

// 选择缓存目录
const selectCacheDirectory = async () => {
  try {
    const result = await ipc.invoke(ipcApiRoute.selectCacheDirectory, {});
    if (result.status) {
      cacheAddress.value = result.data;
      ElMessage.success(result.message || '缓存目录设置成功');
    } else {
      ElMessage.warning(result.message || '未选择目录');
    }
  } catch (error) {
    ElMessage.error('选择缓存目录失败');
    console.error('选择缓存目录失败:', error);
  }
};

// 监听表单变化并更新设置
const watchFormChanges = () => {
  // 监听开机自启
  watch(
    () => form.value.powerBoot,
    async (newValue) => {
      await updateSetting('powerBoot', newValue);
    }
  );

  // 监听最小化到托盘
  watch(
    () => form.value.minimizeToTray,
    async (newValue) => {
      await updateSetting('minimizeToTray', newValue);
    }
  );

  // 监听托盘消息
  watch(
    () => form.value.trayMessage,
    async (newValue) => {
      await updateSetting('trayMessage', newValue);
    }
  );

  // 监听消息提示
  watch(
    () => form.value.messagePrompt,
    async (newValue) => {
      await updateSetting('messagePrompt', newValue);
    }
  );

  // 监听提示声音
  watch(
    () => form.value.promptSound,
    async (newValue) => {
      await updateSetting('promptSound', newValue);
    }
  );

  // 监听页面缩放
  watch(
    () => form.value.pageZoom,
    async (newValue) => {
      await updateSetting('pageZoom', newValue);
    }
  );
};

// 更新单个设置
const updateSetting = async (key: string, value: boolean) => {
  try {
    const result = await ipc.invoke(ipcApiRoute.updateSystemSettings, { key, value });
    if (result.status) {
      ElMessage.success(result.message || '设置更新成功');
    } else {
      ElMessage.error(result.message || '设置更新失败');
      // 如果更新失败，恢复原值
      await loadSystemSettings();
    }
  } catch (error) {
    ElMessage.error('设置更新失败');
    console.error('设置更新失败:', error);
    // 如果更新失败，恢复原值
    await loadSystemSettings();
  }
};

// 在组件挂载后启动监听
onMounted(() => {
  // 延迟启动监听，避免初始化时触发
  setTimeout(() => {
    watchFormChanges();
  }, 1000);
});
</script>
<style lang="scss" scoped></style>
