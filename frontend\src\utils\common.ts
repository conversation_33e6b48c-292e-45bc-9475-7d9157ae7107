interface SearchableItem {
  children?: SearchableItem[];
  [key: string]: any;
}

export function searchCollection(collection: SearchableItem[] | null | undefined, keyword: string, deepSearch: boolean): SearchableItem[] {
  const results: SearchableItem[] = [];
  if (!collection || !Array.isArray(collection)) return results;

  const lowerKeyword = keyword.toLowerCase();

  for (const item of collection) {
    // 检查当前项是否匹配关键字
    let isMatched = false;
    if (item !== null && typeof item === 'object') {
      // 处理对象，检查所有属性值（包括嵌套对象）
      isMatched = checkObject(item, lowerKeyword);
    } else {
      // 处理原始类型（字符串、数字等）
      isMatched = String(item).toLowerCase().includes(lowerKeyword);
    }

    if (isMatched) {
      results.push(item);
    }

    // 深度查找子元素（假设子元素在`children`数组中）
    if (deepSearch && item && typeof item === 'object' && Array.isArray(item.children)) {
      const childResults = searchCollection(item.children, keyword, deepSearch);
      results.push(...childResults);
    }
  }

  return results;
}

// 递归检查对象及其嵌套属性值
function checkObject(obj: any, lowerKeyword: string): boolean {
  for (const key in obj) {
    const value = obj[key];
    if (typeof value === 'string') {
      if (value.toLowerCase().includes(lowerKeyword)) return true;
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      if (String(value).toLowerCase().includes(lowerKeyword)) return true;
    } else if (value !== null && typeof value === 'object') {
      // 递归检查嵌套对象或数组
      if (checkObject(value, lowerKeyword)) return true;
    }
  }
  return false;
}

/**
 * 安全地执行IPC调用，自动处理不可克隆对象错误
 * @param ipcMethod IPC方法（如 ipc.invoke）
 * @param channel IPC通道
 * @param data 要传递的数据
 * @returns Promise
 */
export async function safeIpcCall(ipcMethod: Function, channel: string, data?: any): Promise<any> {
  try {
    return await ipcMethod(channel, data);
  } catch (error) {
    if (error instanceof Error && error.message.includes('could not be cloned')) {
      console.warn('检测到对象克隆错误，尝试清理数据后重新调用:', error.message);
      
      // 动态导入清理函数以避免循环依赖
      const { sanitizeForIpc } = await import('./ipcRequest');
      const sanitizedData = sanitizeForIpc(data);
      
      console.log('使用清理后的数据重新调用IPC:', sanitizedData);
      return await ipcMethod(channel, sanitizedData);
    }
    throw error;
  }
}
