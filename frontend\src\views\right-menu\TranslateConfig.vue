<template>
  <div class="translate-config">
    <div class="header-container">
      <div class="header-title">
        <el-text tag="b" size="large">{{ t('translate.settings') }}</el-text>
        <el-popconfirm :title="t('translate.clearCacheTitle')" :confirm-button-text="t('common.confirm')" :cancel-button-text="t('common.cancel')" @confirm="cleanMsgCache" trigger="hover" width="200">
          <template #reference>
            <el-icon class="header-icon">
              <CleanIcon />
            </el-icon>
          </template>
        </el-popconfirm>
      </div>
    </div>
    <div class="content-container-radio-group">
      <div class="content-left">
        <el-text>{{ t('translate.mode') }}</el-text>
        <el-tooltip effect="dark" placement="top">
          <template #content>
            <div style="max-width: 180px">{{ t('translate.tooltipContent') }}</div>
          </template>
          <el-icon><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="content-right">
        <el-radio-group v-model="configInfo.mode" size="small">
          <el-radio-button :label="t('translate.localTranslate')" value="local" />
          <el-radio-button :label="t('translate.cloudTranslate')" value="cloud" />
        </el-radio-group>
      </div>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('translate.route') }}</el-text>
      </div>
      <div class="content-right">
        <el-select v-model="configInfo.translateRoute" :placeholder="t('translate.selectRoute')" size="default" style="width: 100%">
          <el-option v-for="item in menuStore.translationRoute" :key="item.name" :label="item[currentLanguageName]" :value="item.name" />
        </el-select>
      </div>
    </div>
    <div class="content-container-radio">
      <div class="content-left">
        <el-text>{{ t('translate.realTimeReceive') }}</el-text>
      </div>
      <div class="content-right">
        <el-switch :active-value="'true'" :inactive-value="'false'" size="default" v-model="configInfo.receiveTranslateStatus" />
      </div>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('translate.sourceLanguage') }}</el-text>
      </div>
      <div class="content-right">
        <el-select v-model="configInfo.receiveSourceLanguage" :placeholder="t('translate.sourceLanguage')" size="default" style="width: 100%">
          <el-option :label="t('translate.autoDetect')" value="auto" />
          <el-option v-for="item in languageList" :key="item.code" :label="item[currentLanguageName]" :value="item.code" />
        </el-select>
      </div>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('translate.targetLanguage') }}</el-text>
      </div>
      <div class="content-right">
        <el-select v-model="configInfo.receiveTargetLanguage" placeholder="" size="default" style="width: 100%">
          <el-option v-for="item in languageList" :key="item.code" :label="item[currentLanguageName]" :value="item.code" />
        </el-select>
      </div>
    </div>
    <el-divider />
    <div class="content-container-radio">
      <div class="content-left">
        <el-text>{{ t('translate.realTimeSend') }}</el-text>
      </div>
      <div class="content-right">
        <el-switch :active-value="'true'" :inactive-value="'false'" size="default" v-model="configInfo.sendTranslateStatus" />
      </div>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('translate.sourceLanguage') }}</el-text>
      </div>
      <div class="content-right">
        <el-select v-model="configInfo.sendSourceLanguage" :placeholder="t('translate.sourceLanguage')" size="default" style="width: 100%">
          <el-option :label="t('translate.autoDetect')" value="auto" />
          <el-option v-for="item in languageList" :key="item.code" :label="item[currentLanguageName]" :value="item.code" />
        </el-select>
      </div>
    </div>
    <div class="content-container-select">
      <div class="content-left">
        <el-text>{{ t('translate.targetLanguage') }}</el-text>
      </div>
      <div class="content-right">
        <el-select v-model="configInfo.sendTargetLanguage" placeholder="" size="default" style="width: 100%">
          <el-option v-for="item in languageList" :key="item.code" :label="item[currentLanguageName]" :value="item.code" />
        </el-select>
      </div>
    </div>
    <el-divider />
    <div class="content-container-radio">
      <div class="content-left">
        <el-text>{{ t('translate.friendIndependent') }}</el-text>
      </div>
      <div class="content-right">
        <el-switch :active-value="'true'" :inactive-value="'false'" :disabled="configInfo.showAloneBtn === 'false'" size="default" v-model="configInfo.friendTranslateStatus" />
      </div>
    </div>
    <div class="content-container-radio">
      <div class="content-left">
        <el-text>{{ t('translate.preview') }}</el-text>
      </div>
      <div class="content-right">
        <el-switch :active-value="'true'" :inactive-value="'false'" size="default" v-model="configInfo.translatePreview" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref, unref, watch } from 'vue';
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from '@/api';
import CleanIcon from '@/components/icons/CleanIcon.vue';
import { useMenuStore } from '@/stores/menuStore';
import { useSessionStore } from '@/stores/sessionStore';
import { ElMessage } from 'element-plus';
import { QuestionFilled } from '@element-plus/icons-vue';
import { useI18n } from 'vue-i18n';

const menuStore = useMenuStore();
const sessionStore = useSessionStore();
const configInfo = ref({});
const { t, locale } = useI18n();

// 修改计算属性名称使其更通用
const currentLanguageName = computed(() => {
  return locale.value === 'zh' ? 'zhName' : 'enName';
});

watch(
  () => configInfo.value.friendTranslateStatus,
  async (newValue, oldValue) => {
    if (newValue && oldValue) {
      const res = await ipc.invoke(ipcApiRoute.changeAloneStatus, {
        partitionId: menuStore.currentPartitionId,
        status: newValue
      });
      if (res.status) {
        await getConfigInfo();
      }
    }
  }
);
watch(
  () => menuStore.currentPartitionId,
  async (newValue, oldValue) => {
    if (newValue) {
      await getConfigInfo();
    }
  }
);

// 定义需要监听的字段
const propertiesToWatch = ['mode', 'translateRoute', 'receiveTranslateStatus', 'receiveSourceLanguage', 'receiveTargetLanguage', 'sendTranslateStatus', 'sendSourceLanguage', 'sendTargetLanguage', 'chineseDetectionStatus', 'translatePreview'];

let watchers = []; // 存储所有字段的监听器

// 初始化字段监听逻辑
const addWatchers = () => {
  removeWatchers(); // 确保不会重复绑定监听器
  watchers = propertiesToWatch.map((property) =>
    watch(
      () => unref(configInfo.value[property]),
      (newValue, oldValue) => {
        if (newValue !== '' && newValue !== oldValue) {
          handlePropertyChange(property, newValue);
        }
      }
    )
  );
};
// 自定义逻辑
const handlePropertyChange = async (property, value) => {
  const args = { key: property, value: value, id: configInfo.value.id, partitionId: menuStore.currentPartitionId };
  await ipc.invoke(ipcApiRoute.updateTranslateConfig, args);
};
// 移除所有字段的监听器
const removeWatchers = () => {
  watchers.forEach((stopWatcher) => stopWatcher()); // 调用每个监听器的停止方法
  watchers = [];
};
const getConfigInfo = async () => {
  removeWatchers();
  const platform = await menuStore.platform;
  const partitionId = await menuStore.currentPartitionId;
  try {
    const args = {
      platform: menuStore.platform,
      userId: '',
      partitionId: menuStore.currentPartitionId
    };
    const res = await ipc.invoke(ipcApiRoute.getTrsConfig, args);
    if (res.status) {
      Object.assign(configInfo.value, res.data); // 更新表单数据
    } else {
      // console.log(res);
    }
  } catch (err) {
  } finally {
    addWatchers();
  }
};
onMounted(() => {
  getConfigInfo();
});
// 清理缓存
const cleanMsgCache = async () => {
  try {
    // 获取当前会话的partitionId
    const currentSession = sessionStore.currentSession;
    if (!currentSession) {
      ElMessage.warning('请先选择一个会话');
      return;
    }

    // 清理当前会话的翻译缓存
    const result = await ipc.invoke('controller/translate/clearSessionCache', {
      partitionId: currentSession.partitionId
    });

    if (result && result.status) {
      ElMessage.success('翻译缓存清理成功');
    } else {
      ElMessage.error(result?.message || '清理失败');
    }
  } catch (error) {
    console.error('清理翻译缓存失败:', error);
    ElMessage.error('清理翻译缓存失败');
  }
};

// 语言列表
const languageList = ref([]);
const getLanguageList = async () => {
  const res = await ipc.invoke(ipcApiRoute.getLanguageList, {});
  // console.log(res)
  if (res.status) {
    languageList.value = res.data;
  } else {
    ElMessage({
      message: `${res.message}`,
      type: 'error',
      offset: 40
    });
  }
};
onMounted(() => {
  getLanguageList();
});
// 生命周期
onMounted(() => {
  ipc.removeAllListeners('translate-config-update');
  ipc.on('translate-config-update', (event, args) => {
    const { data } = args;
    configInfo.value = data;
  });
});

onUnmounted(() => {
  ipc.removeAllListeners('translate-config-update');
});
</script>

<style scoped lang="scss">
.translate-config {
  width: 300px;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  :deep(.el-text) {
    --el-text-color: var(--el-text-color-primary);
  }
  :deep(.el-divider--horizontal) {
    margin: 14px 0;
  }
  .header-container {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    user-select: none;
    justify-content: flex-start;

    .header-title {
      display: flex;
      flex: 1;
      height: 30px;
    }
    .header-icon {
      height: 30px;
      display: flex;
      margin-left: 5px;
      cursor: pointer;
    }
  }
  .content-container-select {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    :deep(.el-select__wrapper) {
      border-radius: 0;
    }
    .content-left {
      height: 50px;
      align-items: center;
      display: flex;
      flex: 1;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 2;
      align-items: center;
    }
  }
  .content-container-radio {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .content-left {
      height: 50px;
      align-items: center;
      display: flex;
      flex: 2;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 1;
      align-items: center;
    }
  }
  .content-container-radio-group {
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .content-left {
      height: 50px;
      align-items: center;
      display: flex;
      flex: 1;
      user-select: none;
    }
    .content-right {
      height: 50px;
      display: flex;
      flex: 2;
      align-items: center;
    }
  }
}
</style>
