{"name": "ee", "version": "4.0.0", "type": "module", "scripts": {"dev": "vite --host --port 8080", "serve": "vite --host --port 8080", "build-staging": "vite build --mode staging", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "format": "prettier --write \"./src/**/*.{ts,vue}\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "crypto-js": "^4.2.0", "element-plus": "^2.8.6", "keytar": "^7.9.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.2.6", "qrcode.vue": "^3.6.0", "vue": "^3.5.12", "vue-i18n": "^9.0.0", "vue-router": "^4.0.14"}, "devDependencies": {"@iconify/json": "^2.2.357", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.9.0", "@types/nprogress": "^0.2.3", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@vitejs/plugin-vue": "^4.2.3", "@vue/compiler-sfc": "^3.2.33", "@vueuse/core": "^13.5.0", "prettier": "^3.6.2", "sass": "^1.89.2", "sass-loader": "^16.0.5", "terser": "^5.19.1", "typescript": "^5.6.3", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^5.4.11", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.1.6"}}